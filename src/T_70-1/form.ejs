<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>TDR0007_目标拖拽_中文版</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src='./form/js/vue.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="h-title">TDR0007_目标拖拽_中文版</h3>
      <!-- 公共区域 -->
      <% include ./src/common/template/common_head_nontitle_cn %>
      <!-- 题干音频 -->
      <div class="c-group">
        <div class="c-title">题干音频</div>
        <div class="c-area">
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label label-bg-img" for=""><span>题干音频<em>*</em></span></label>
              <label for="isVideo" class="inline-label">
                <input type="radio" name="isVideo" value="1" v-model="configData.source.isVideo">&nbsp;无题干音频
              </label>
              <label for="isVideo" class="inline-label">
                <input type="radio" name="isVideo" value="2" v-model="configData.source.isVideo">&nbsp;有题干音频
              </label>
            </div>
          </div>
        </div>
        <div class="c-area" v-if="configData.source.isVideo == 2">
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label label-bg-img" for="">音频文件<em>*</em></label>
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-videoUrl"
                v-on:change="audioUpload($event,configData.source,'videoUrl')">
              <label for="content-videoUrl" class="btn btn-show upload" v-if="!configData.source.videoUrl">上传</label>
              <div class="audio-preview" v-show="configData.source.videoUrl">
                <div class="audio-tools">
                  <p v-show="configData.source.videoUrl">{{configData.source.videoUrl}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="configData.source.videoUrl"></audio>
                </span>
              </div>
              <span class='txt-info'>大小：≤30KB</span>
              <label for="content-videoUrl" class="btn upload btn-audio-dele" v-if="configData.source.videoUrl"
                @click="configData.source.videoUrl=''">删除</label>
              <label for="content-videoUrl" class="btn upload re-upload" v-if="configData.source.videoUrl">重新上传</label>
            </div>
          </div>

          <div class="c-well">
            <label>
              <span class="game_span label-bg-img">音频位置<em>*</em></span>
              X:<input type="number" class="c-input-txt"
                style="margin: 0 10px;width: 60px!important; display: inline-block;"
                oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="configData.source.videoX">
              Y:<input type="number" class="c-input-txt"
                style="margin: 0 10px;width: 60px!important; display: inline-block;"
                oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="configData.source.videoY">
              <span class="game-title">(数字，0<=x<=1920,0<=y<=1080)< /span>
                  <span class="game-title">(题干图标的Z轴层级比较靠后，可能被选项覆盖住。)</span>
            </label>
          </div>

          <div class="c-well">
            <label>
              <span class="game_span label-bg-img">题干声音长度<em>*</em></span>
              <input type="number" class="c-input-txt"
                style="margin: 0 10px;width: 60px!important; display: inline-block;"
                v-model="configData.source.videoDuration">毫秒
              <span class='add-txt-info'>1秒=1000毫秒</span>
            </label>
          </div>

        </div>
      </div>

      <!-- 开场方式 -->
      <div class="c-group">
        <div class="c-title">开场方式</div>
        <div class="c-area">
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label label-bg-img label-wrap-top" for=""><span>开场方式<em>*</em></span></label>
              <div class="field-label-content">
                <label for="openWay" class="inline-label">
                  <input type="radio" name="openWay" value="1" v-model="configData.source.openWay">&nbsp;
                  游戏开场时，所有选项默认位于起点位置。
                </label>
                <label for="openWay" class="inline-label">
                  <input type="radio" name="openWay" value="2" v-model="configData.source.openWay">&nbsp;
                  游戏开场时，所有选项从终点位置自动飞到起点位置，再开始游戏。（先显示了答案）
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 结局方式 -->
      <div class="c-group">
        <div class="c-title">结局方式</div>
        <div class="c-area">
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label label-bg-img" for=""><span>结局方式<em>*</em></span></label>
              <label for="isEnding" class="inline-label">
                <input type="radio" name="isEnding" value="1" v-model="configData.source.isEnding">&nbsp;出现结局页
              </label>
              <label for="isEnding" class="inline-label">
                <input type="radio" name="isEnding" value="2" v-model="configData.source.isEnding">&nbsp;没有结局页
              </label>
            </div>
          </div>
        </div>
        <div class="c-area" v-if="configData.source.isEnding == 1">
          <div class="c-well img-upload">
            <div class="field-wrap">
              <label class="field-label label-bg-img" for="">结局页雪碧图<em>*</em></label>
              <input type="file" v-bind:key="Date.now()" class="btn-file" id="endFigure" size="1920*640"
                v-on:change="imageUpload($event,configData.source,'endFigure',50)">
              <label for="endFigure" class="btn btn-show upload"
                v-if="configData.source.endFigure==''?true:false">上传</label>
              <label for="endFigure" class="btn upload re-upload"
                v-if="configData.source.endFigure!=''?true:false">重新上传</label>
              <span class='txt-info'>大小≤50KB,3帧动画，尺寸：1920*640</span>
            </div>
            <div class="img-preview" v-if="configData.source.endFigure!=''?true:false">
              <img v-bind:src="configData.source.endFigure" alt="" />
              <div class="img-tools">
                <span class="btn btn-delete" v-on:click="configData.source.endFigure=''">删除</span>
              </div>
            </div>
          </div>
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label label-bg-img" for="">结局页音频<em>*</em></label>
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-endVideo"
                v-on:change="audioUpload($event,configData.source,'endVideo')">
              <label for="content-endVideo" class="btn btn-show upload" v-if="!configData.source.endVideo">上传</label>
              <div class="audio-preview" v-show="configData.source.endVideo">
                <div class="audio-tools">
                  <p v-show="configData.source.endVideo">{{configData.source.endVideo}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="configData.source.endVideo"></audio>
                </span>
              </div>
              <span class='txt-info'>大小：≤30KB</span>
              <label for="content-endVideo" class="btn upload btn-audio-dele" v-if="configData.source.endVideo"
                @click="configData.source.endVideo=''">删除</label>
              <label for="content-endVideo" class="btn upload re-upload" v-if="configData.source.endVideo">重新上传</label>
            </div>
          </div>
          <div class="c-well">
            <label>
              <span class="game_span label-bg-img">音频长度<em>*</em></span>
              <input type="number" class="c-input-txt"
                style="margin: 0 10px;width: 60px!important; display: inline-block;"
                v-model="configData.source.endDuration">毫秒
              <span class='add-txt-info'>1秒=1000毫秒</span>
            </label>
          </div>
        </div>
      </div>

      <!--  选项 -->
      <div class="c-group">
        <div class="c-title">选项</div>
        <div class="c-group-title">-可设置1～6组选项，可含干扰项</div>
        <div class="c-group-title">-若设定的宽高和图片的原比例不同，则发生图片拉伸</div>
        <div v-for="(item,index) in configData.source.options">
          <div class="options-field-top">
            <label class="field-label field-label-text" for="">选项{{index+1}}</label>
            <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>1"></span>
          </div>
          <div class="c-area">
            <div class="c-well">
              <span class="game_span label-bg-img">选项类型</span>
              <label for="isRightAnswers" class="inline-label">
                <input type="radio" :name="'isRightAnswers'+index" value="1" v-model="item.isRightAnswers">&nbsp;正确答案
              </label>
              <label for="isRightAnswers" class="inline-label">
                <input type="radio" :name="'isRightAnswers'+index" value="2" v-model="item.isRightAnswers">&nbsp;干扰项
              </label>
            </div>
            <div class="c-well img-upload">
              <!-- 上传图片 -->
              <div class="field-wrap">
                <label class="field-label label-bg-img" for=""><span class='txt-info'>上传图片<em>*</em></span></label>
                <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size=""
                  v-on:change="imageUpload($event,item,'img',30, index)">
                <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label>
                <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img">重新上传</label>
                <span class='txt-info'>尺寸、比例自定义，大小≤30KB</span>
              </div>
              <div class="img-preview" v-if="item.img">
                <img v-bind:src="item.img" alt="" />
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                </div>
                <span class="c-input-disabled">
                  该图片尺寸:
                  <input type="text" :value="item.natWidth" disabled>*
                  <input type="text" :value="item.natHeight" disabled>
                </span>
              </div>
            </div>
          </div>
          <div class="c-area">
            <!-- 起点位置 -->
            <div class="c-well">
              <label>
                <span class="game_span label-bg-img">图片起点位置<em>*</em></span>
                X:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.imgStartX">
                Y:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.imgStartY">
                <span class="game-title">(数字，0<=x<=1920,0<=y<=1080) </span>
              </label>
            </div>
            <!-- 选项大小 -->
            <div class="c-well">
              <label>
                <span class="game_span label-bg-img">图片起点大小<em>*</em></span>
                 height:<input type="number" class="c-input-txt"
                  style="margin: 0 5px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.imgStartHeight">
                width:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.imgStartWidth">
              </label>
            </div>
          </div>
          <div class="c-area" v-if="item.isRightAnswers == 1">
            <!-- 终点位置 -->
            <div class="c-well">
              <label>
                <span class="game_span label-bg-img">图片终点位置<em>*</em></span>
                X:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.imgEndX">
                Y:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.imgEndY">
                <span class="game-title">(数字，0<=x<=1920,0<=y<=1080) </span>
              </label>
            </div>
            <!-- 选项大小 -->
            <div class="c-well">
              <label>
                <span class="game_span label-bg-img">图片终点大小<em>*</em></span>
                height:<input type="number" class="c-input-txt"
                  style="margin: 0 5px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.imgEndHeight">
                    width:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.imgEndWidth">
              </label>
            </div>
          </div>
          <!-- 正确反馈区域 -->
          <div class="c-area" v-if="item.isRightAnswers == 1">
            <div class="c-well">
              <label>
                <div>正确反馈区域<em>*</em></div>
              </label>
              <label>
                <span class="game_span label-bg-img">区域左上角坐标<em>*</em></span>
                X:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.areaX">
                Y:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.areaY">
                <span class="game-title">(数字，0<=x<=1920,0<=y<=1080) </span>
              </label>
            </div>
            <div class="c-well">
              <label>
                <span class="game_span label-bg-img">区域大小<em>*</em></span>
                height:<input type="number" class="c-input-txt"
                  style="margin: 0 5px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.areaHeight">
                    width:<input type="number" class="c-input-txt"
                  style="margin: 0 10px;width: 60px!important; display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.areaWidth">
              </label>
            </div>
          </div>
        </div>
        <!-- v-show="configData.source.options.length<6" -->
        <button type="button" class="add-tg-btn" v-on:click="addOption({
                      isRightAnswers:'1',
                      img:'',
                      natWidth:'',
                      natHeight:'',
                      imgStartX:'',
                      imgStartY:'',
                      imgStartWidth: '',
                      imgStartHeight: '',
                      imgEndX: '',
                      imgEndY: '',
                      imgEndWidth: '',
                      imgEndHeight: '',
                      areaX: '',
                      areaY: '',
                      areaWidth: '',
                      areaHeight:''})">+</button>
      </div>

      <button class="send-btn" v-on:click="onSend">提交</button>

    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/logo_bj.png?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
