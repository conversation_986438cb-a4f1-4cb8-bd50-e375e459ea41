import { nodeName } from "jquery";

/**
 * 是否进入demo模式同时缩放模板
 * show:是否展示demo模式
 * className：缩放模版的类或id名称
 */
export function templateScale(msg) {
    // 是否显示缩放模版
    let show = msg.show == true ? true : false;
    // 需要添加缩放模版的类
    let cls = msg.className
    if (show) {
        $(".hint-content").show();
        cls.addClass("hint-container");
    } else {
        $(".hint-content").hide();
        cls.removeClass("hint-container");
    }
}


/**
 * 显示并小手点击
 * 注意: 1. 本方法不计算模板外的白色区域，请模板自己计
 *       2.整个动画持续时间为2.6秒  因此调用handClick后至少保留2.6s的时间才能隐藏小手;否则会导致小手退出动画无法播放完毕，再次播放时动画开始位置并不是初始位置。
 * postionLeft：选中当前的节点的x轴距离
 * postionTop：选中当前的节点的Y轴距离
 */
export function handClick(postionTop, postionLeft) {
    // 当前手型移动到当前位置
    $('.hand').removeClass('hide');

    //手移入可视区
    document.styleSheets[0].insertRule(`@keyframes moveIn {100%{ animation-timing-function: linear;left: ${postionLeft};top: ${postionTop};}}`); //写入样式
    $('.hand').css({
        'animation': `moveIn 0.8s 1 linear`,
        'animation-fill-mode': 'forwards'
    });
    setTimeout(() => {
        $('.hand').css({
            'animation': ``,
            top: postionTop,
            left: postionLeft
        })
        //点击
        $('.hand').css({
            'animation': `handClick 1s steps(3) 1`,
            'animation-fill-mode': 'forwards'
        });
        //手退出可视区
        setTimeout(() => {
            $('.hand').css({
                'animation': `moveOut 0.8s 1 linear`,
                'animation-fill-mode': 'forwards'
            });
            setTimeout(() => {
              //小手退出后，复位到原始位置；不要着急隐藏小手，一定要确保本处代码被执行，否则再次播放时动画小手位置并不是初始位置。
                $('.hand').css({
                    'animation': ``,
                    'left': '150vh',
                    'top': '100vh'
                });
            }, 800);
        }, 1000);
    }, 800);
}

/**
 * 显示并小手拖拽
 * 注意: 1. 本方法不计算模板外的白色区域，请模板自己计
 *       2.整个动画持续时间为2.6秒  因此调用handClick后至少保留2.6s的时间才能隐藏小手;否则会导致小手退出动画无法播放完毕，再次播放时动画开始位置并不是初始位置。
 * postionLeft：选中当前的节点的x轴距离
 * postionTop：选中当前的节点的Y轴距离
 */
 export function handDrag(startTop, startLeft, endTop, endLeft) {
  // 当前手型移动到当前位置
  $('.hand').removeClass('hide');

  //手移入可视区
  document.styleSheets[0].insertRule(`@keyframes moveIn {100%{ animation-timing-function: linear;left: ${startLeft};top: ${startTop};}}`); //写入样式
  // $('.hand').css({
  //     'animation': `moveIn 0.8s 1 linear`,
  //     'animation-fill-mode': 'forwards'
  // });
  // setTimeout(() => {
      $('.hand').css({
        'animation': '',
        top: startTop,
        left: startLeft
      });
      //手样子
      // $('.hand').css({
      //     'animation': 'handClick .5s steps(3) .5s',
      //     'animation-fill-mode': 'forwards'
      // });
      $('.hand').css({
        animation: 'none',
        backgroundPosition: '200% 0',
      });
      setTimeout(() => {
        $('.hand').css({
          animation: 'none',
          top: endTop,
          left: endLeft,
          backgroundPosition: '200% 0',
        });
        // $(".options-list ul li").eq(0).css({
        //   top: endTop/100 + 'rem',
        //   left: endLeft/100 + 'rem'
        // })
        // setTimeout(() => {
        //   $(".options-list ul li").eq(0).css({
        //     top: startTop/100 + 'rem',
        //     left: startLeft/100 + 'rem'
        //   })
        //   $('.hand').addClass('hide');
        // },1000)
      }, 1200)
  // }, 500);
}


/**
 * 隐藏小手
 * @param
 * @returns
 * from <EMAIL>
 */
export function handHide(isHide) {
    if (isHide) {
        $('.hand').addClass('hide');
    }

}
