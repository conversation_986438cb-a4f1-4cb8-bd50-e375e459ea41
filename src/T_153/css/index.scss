@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import "../../common/template/multyDialog/style.scss";

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}
* {
  box-sizing: border-box;
}
.desc-visi {
  visibility: hidden;
}
.container {
  // background-image: url(../image/1.jpg);
  position: relative;
  // font-family:"ARLRDBD";
}
audio {
  width: 0;
  height: 0;
  opacity: 0;
}
.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  .audioSrc {
    position: absolute;
  }
}
.mainArea {
  width: 16.4rem;
  height: 7.45rem;
  // position: relative;
  top: 2.15rem;
  left: 50%;
  margin-left: -8.2rem;
  .list {
    width: 1.64rem;
    height: 1.49rem;
    // float: left;
    box-sizing: border-box;
    // position: relative;
    position: absolute;
    z-index: 10;
    .audioList {
      position: absolute;
      width: 1.45rem;
      height: 1.34rem;
      background: url("../image/btn-audio-bg.png") no-repeat;
      background-size: 100% 100%;
      z-index: 10;
      left: 50%;
      top: 50%;
      transform: translateX(-50%) translateY(-50%);
      cursor: pointer;
      img {
        position: absolute;
        top: 0.3rem;
        left: 0.3rem;
        width: 0.83rem;
        height: 0.8rem;
      }
      audio {
        width: 0;
        height: 0;
        opacity: 0;
        position: absolute;
      }
    }
  }
}

.game-container {
  width: 100%;
  height: 100%;
  position: relative;

  .experience {
    position: absolute;
    z-index: 7;
    left: 50%;
    top: 0.4rem;
    transform: translateX(-50%);
    width: 5.36rem;
    height: 0.4rem;
    .chest {
      position: absolute;
      // left: 0;
      // top: 1rem;
      width: 0.87rem;
      height: 0.8rem;
      background: url("../image/chest.png") no-repeat center;
      background-size: cover;
      left: 91%;
      bottom: -20%;
      z-index: 10;
    }
    .chest-half {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 0.51rem;
      height: 0.47rem;
      background: url("../image/chest.png") no-repeat center;
      background-size: cover;
      z-index: 10;
      // left: 33.33%;
      display: none;
    }
    .horn {
      position: absolute;
      z-index: 10;
      left: 0;
      top: -30%;
      transform: translate(-100%, -50%);
      // background-color:red;
      width: 0.6rem;
      height: 0.6rem;
    }
  }
  .experience-bar {
    // position: absolute;
    // z-index: 7;
    // left: 50%;
    // top: 1rem;
    // transform: translateX(-50%);
    // width: 5.36rem;
    // height: 0.4rem;
    width: 100%;
    height: 100%;
    background-color: RGBA(126, 78, 59, 1);
    border-radius: 0.24rem;
    overflow: hidden;
    padding: 2px;
    border: 0.06rem solid #fff;

    .experience-bar-fill {
      height: 100%;
      width: 0;
      transition: width 1s ease;
      background: url("../image/experience.png") repeat center;
      background-size: cover;
      border-radius: 0.24rem;
    }
  }

  .energy-ball {
    position: fixed;
    z-index: 15;
    background: url(../image/energy.png) no-repeat;
    background-size: 100% 100%;
    width: 1.91rem;
    height: 1.91rem;
    left: 1.2rem;
    top: 1.9rem;
    display: none;
    transform: translate(-50%,-50%);
  }

  .game-item {
    position: absolute;
    z-index: 1;
    // background-color: red;
    width: 4.26rem;
    height: 5.51rem;
    .ani-wrap {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;
    }
    .item-wrap {
      position: relative;
      // z-index: 5;
      height: 5.07rem;
      // overflow: hidden;
      // display: none;
      .content-card {
        width: 100%;
        height: 3.06rem;
        background: no-repeat  center;
        background-size: cover;
        transform: scale(0);
        transition: transform .5s ease;
        cursor: pointer;
        position: relative;
        z-index: 5;
        .content-energy {
          position: absolute;
          width: 1px;
          height: 1px;
          z-index: 16;
          bottom: 0;
          left: 50%;
          transform: translate(-50%,50%);
        }
      }
      .ani {
        // width: 2.56rem;
        width: 100%;
        height: 2rem;
        position: absolute;
        // left: 0.82rem;
        left: 0;
        z-index: 5; 
        bottom: 0;
        transform: translateY(100%);
        transition: transform .5s ease;
        .breath {
          position: absolute;
          left: 50%;
          top: 0;
          // display: none;
          transform: translateX(-50%);
        }
        .yun {
          position: absolute;
          left: 50%;
          top: 0;
          transform: translateX(-50%);
          display: none;
        }
      }
      &.active {
        display: block;
        .ani {
          transform: translateY(0);
        }
        .content-card {
          transform: scale(1);
        }
      }
    }
    .after-ani-bg {
      position: absolute;
      // left: 0.47rem;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0;
      z-index: 1;
      width: 3.06rem;
      height: 0.93rem;
      background: no-repeat center;
      background-size: cover;
    }

    .before-ani-bg {
      position: absolute;
      // left: 0.59rem;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0.18rem;
      z-index: 10;
      width: 3.09rem;
      height: 0.6rem;
      background: no-repeat center;
      background-size: cover;
    }

    .hammer {
      position: absolute;
      z-index: 11;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 2.4rem;
      height: 2.54rem;
      right: -0.5rem;
      top: 0.5rem;
      transform-origin: right bottom;
      display: none;
      &.hammerPlay {
        animation: hammerPlay .3s forwards;
      }
    }
    .caidai {
      position: absolute;
      bottom: 0;
      display: none;
      z-index: 16;
    }
  }
}

.funcMask {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 21;
  display: none;


  .startBtn {
    width: 2.03rem;
    height: auto;
    cursor: pointer;
    position: absolute;
    bottom:1.68rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .startBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 7.02rem;
    width: 10.5rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0.5rem;

    .demoTextBox {
      width: 9.26rem;
      height: 3.9rem;
      position: absolute;
      left: 50%;
      top: 0.64rem;
      transform: translate(-50%);
      border-radius: 0.3rem;
      background: rgba(0, 0, 0, 0.2);
      text-align: center;

      .startMsg {
        width: 6.75rem;
        height: 2.88rem;
        margin-top: 0.4rem;
      }
    }

    .demoBtnBox {
      width: 5.72rem;
      height: 1.14rem;
      transform: translate(-50%);
      // background: rgba(0, 0, 0, 0.2);
      text-align: center;
      position: absolute;
      left: 50%;
      bottom: 0.75rem;

      .demo-btnStu {
        width: 2.14rem;
        height: auto;
        cursor: pointer;
        display: inline-block;
      }

    //   .startBtn {
    //     width: 2.03rem;
    //     height: auto;
    //     cursor: pointer;
    //     display: inline-block;
    //     margin-right: 0;
    //     //  margin-left: 1.55rem;
    //     top: 50%;
    //     left: 50%;
    //     transform: translate(-50%,-50%);
    //   }
    }
  }

  .timeChangeBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 4.8rem;
    width: 7.2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.5rem;

    .timeBg {
      width: 3.79rem;
      height: 3.84rem;
      position: absolute;
      top: 1rem;
      background: url(../image/timeBg.png) no-repeat;
      background-size: 100% 100%;
      left: 50%;
      margin-left: -1.9rem;
      top: 50%;
      margin-top: -1.92rem;

      .numberList {
        width: 1.5rem;
        height: 1.5rem;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        top: 0;
        margin: auto;
        background: url(../image/number1.png) no-repeat;
        background-size: 6rem 100%;
        background-position-x: 0.1rem;
      }
    }
  }
}

.timeChangeBox {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  height: 4.8rem;
  width: 7.2rem;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 0.5rem;

  .timeBg {
    width: 3.79rem;
    height: 3.84rem;
    position: absolute;
    top: 1rem;
    background: url(../image/timeBg.png) no-repeat;
    background-size: 100% 100%;
    left: 50%;
    margin-left: -1.9rem;
    top: 50%;
    margin-top: -1.92rem;

    .numberList {
      width: 1.5rem;
      height: 1.5rem;
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      top: 0;
      margin: auto;
      background: url(../image/number1.png) no-repeat;
      background-size: 6rem 100%;
      background-position-x: 0.1rem;
    }
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0) rotate(0deg);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px) rotate(-5deg);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px) rotate(5deg);
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes shake-hor {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px);
  }
}

.shake-hor {
  animation: shake-hor 0.5s;
  will-change: transform;
}

// 添加动画类
.breathe {
  animation: breathe 3s ease-in-out infinite;
  will-change: transform;
}

.shake {
  animation: shake 0.5s;
  will-change: transform;
}

@keyframes wrong-shake {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  25% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: scale(1);
    opacity: 0.6;
  }
  75% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}
.wrong-animation {
  animation: wrong-shake 0.5s ease-in-out;
}

@keyframes explode {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  80% {
    transform: scale(1.5);
    opacity: 0.7;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.explode {
  animation: explode 0.5s forwards;
}

@keyframes hammerPlay {
  0% {
      transform: rotateZ(20deg);
  }
  100% {
      transform: rotateZ(-50deg);
  }
}