"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, CLASS_STATUS } from "../../common/js/constants.js";
$(function () {
  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "3", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };

  let h5SyncActions = parent.window.h5SyncActions;
  const isSync = h5SyncActions && h5SyncActions.isSync;
  let source = configData.source;
  const { rounds = [], audios = [], imgs = [], dishu = {}, hole = {} } = source;

  const imgsObj = {};
  imgs.forEach((item) => {
    imgsObj[item.id] = item.img;
  });

  const audiosObj = {};
  audios.forEach((item) => {
    audiosObj[item.id] = item.audio;
  });
  let classStatus = "0"; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }

  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/bg.png)" });
  }
  let isFirst = true;

  const page = {
    musicConfig: {
      right: "./audio/right.mp3",
      wrong: "./audio/wrong.mp3",
      experience:'./audio/experience.mp3'
    },

    defaultConfig: {
      horn: "./image/laba.json",
      dishuBreath:'./image/dishu_idle.json',
      dishuYun:'./image/dishu_yun.json',
      dishuCaidai:'./image/dishu_caidai.json',
      holeBefore:'./image/before-ani-bg.png',
      holeAfter:'./image/after-ani-bg.png',
      holeHammer:'./image/hammer.png',
      locations: [
        {
          t: 228,
          l: 70,
        },
        {
          t: 120,
          l: 750,
        },
        {
          t: 228,
          l: 1430,
        },
        {
          t: 484,
          l: 480,
        },
        {
          t: 484,
          l: 1020,
        },
      ],
    },

    // todo 游戏状态
    gameState: {
      currentRound: 0,
      isPlaying: false,
    },

    // todo 随机数据

    randomResArr: [],

    hornAnimation: null,

    breathAnimation:[],
    yunAnimation:[],
    caidaiAnimation:[],

    targetsAllowClick: false,

    init: function () {
      this.isShowMask();
      this.initAnimation();
      this.initPage();
      this.initRule();
    },

    dealConfigData: function () {
      const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        const classStatus =
          SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.IN_CLASS && userType == USER_TYPE.STU) {
          $('.startBtn').hide();
        }
      page.init();
    },

    isShowMask: function () {
      // if (isSync) {
      //   const userType = window.frameElement
      //     ? window.frameElement.getAttribute("user_type")
      //     : "";
      //   const classStatus = SDK.getClassConf().h5Course.classStatus;
      //   if (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU) {
      //     // not in class && student
      //     $(".funcMask").show();
      //   }
      // } else {
      //   $(".funcMask").show();
      // }
      $(".funcMask").show();
    },

    initAnimation: function () {
      this.createHornAnimation();
      this.createBreathAnimation();
      this.createYunAnimation();
      this.createCaidaiAnimation()
    },

    initPage: function () {
      $(".game-container").on("syncInitRule", function (e) {
        console.log("recover ganme-container sdk");
        page.randomResArr = [...SDK.syncData.randomResArr];
        SDK.setEventLock();
      });

      $(".experience-bar-fill").on(
        "transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd",
        function (e) {
          console.log("经验值动画结束", e.originalEvent.propertyName);
          const { currentRound } = page.gameState;
          if (currentRound >= rounds.length - 1) {
            page.gameOver();
          } else {
            // 本轮完成
            page.beforeCompleteRound();
            page.completeRound()
          }
        }
      );

      $(".startBtn").on("click touchstart", function (e) {
        console.log("jf startBtn click");
        if (e.type == "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation();
        if (!isSync) {
          $(this).trigger("sycnStartBtnClick");
          return;
        }
        const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        const classStatus =
          SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.IN_CLASS && userType == USER_TYPE.STU) {
          return;
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "sycnStartBtnClick",
          recoveryMode: "1",
        });
      });

      $(".startBtn").on("sycnStartBtnClick", function () {
        console.log("jf sycnStartBtnClick");
        const classStatus =
          SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
        $(".item-wrap").removeClass("active");
        if(classStatus == CLASS_STATUS.IN_CLASS) {
          console.log('上课状态')  
          moduleAuthorizationFn("stuOnly", "12")
        }
        page.initCommonData();
        page.threeTwoOne();
        SDK.setEventLock();
      });

      page.initDiglett();
      page.mountedCreatEle()
    },

    initDiglett: function () {
      const { before, after ,hammer} = hole;
      const { holeBefore, holeAfter, holeHammer, locations } = page.defaultConfig;
      const effectBefore = before || holeBefore;
      const effectAfter = after || holeAfter;
      const effectHammer = hammer || holeHammer;
      for (let index = 0; index < locations.length; index++) {
        const { t, l } = locations[index];
        const $ele = $(
          `<div class="game-item">
              <div class="item-wrap">
                <div class="content-card" data-syncactions="syncCardClick-${index}">
                  <div class="content-energy"></div>
                </div>
                <div class="ani-wrap">
                  <div class="ani">
                    <div class="breath breath-${index}"></div>
                    <div class="yun yun-${index}"></div>
                  </div>
                </div>
              </div>
              <div class="hammer" data-index=${index} style="background-image: url(${effectHammer})"></div>
              <div class="ani-wrap">
                <div class="after-ani-bg" style="background-image: url(${effectAfter})"></div>
                <div class="before-ani-bg" style="background-image: url(${effectBefore})"></div>
              </div>
              <div class="caidai caidai-${index}"></div>
            </div>`
        ).css({
          top: t / 100 + "rem",
          left: l / 100 + "rem",
        });
        $ele.find('.content-card').on("click touchstart", function (e) {
          console.log('jf cardclick')
          if (e.type == "touchstart") {
            e.preventDefault();
          }
          e.stopPropagation();
  
          if (!isSync) {
            $(this).trigger("syncCardClick");
            return;
          }
  
          SDK.bindSyncEvt({
            index: $(e.currentTarget).data("syncactions"),
            eventType: "click",
            method: "event",
            syncName: "syncCardClick",
            recoveryMode: "1",
          });
        });

        $ele.find('.content-card').on("syncCardClick", function () {
          console.log('jf syncCardClick',$(this).attr('data-right'))
          if($(this).attr('data-right') === '1') {
            // todo 选对
            page.playClickAudio(true)
            $(this).parent().parent().find('.hammer').show().addClass('hammerPlay').on('animationend webkitAnimationEnd',function () {
              $(this).hide().removeClass('hammerPlay').off('animationend webkitAnimationEnd')
              $(this).siblings('.item-wrap').find('.breath').hide()
              $(this).siblings('.item-wrap').find('.yun').show()
              const eleIndex = $(this).attr('data-index')
              if(page.yunAnimation[eleIndex]) {
                lottieAnimations.stop(page.yunAnimation[eleIndex]);
                lottieAnimations.play(page.yunAnimation[eleIndex]);
              }
              $('.game-item').eq(eleIndex).find('.caidai').show()
              console.log(page.caidaiAnimation,eleIndex,'----')
              if(page.caidaiAnimation[eleIndex]) {
                lottieAnimations.stop(page.caidaiAnimation[eleIndex]);
                lottieAnimations.play(page.caidaiAnimation[eleIndex]);
              }
            })
            // page.dealExperienceValue()
          } else {
            // todo 选错
            $(this).addClass('shake-hor');
            page.playClickAudio()
            setTimeout(() => {
              $(this).removeClass('shake-hor');
            }, 500);
          } 
          SDK.setEventLock();
        });
        $(".game-container").append($ele);
      }
    },

    initRule: function () {
      const userType = window.frameElement
        ? window.frameElement.getAttribute("user_type")
        : "";
      const classStatus =
        SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
      if (
        !isSync ||
        (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU)
      ) {
        // page.initAudio();
        page.computeResult();
      } else {
        if (userType === USER_TYPE.TEA) {
          // todo tea coming computed result
          if (
            page.randomResArr.length === 0 &&
            (!SDK.syncData.randomResArr ||
              SDK.syncData.randomResArr.length === 0)
          ) {
            // page.initAudio();
            page.computeResult();
            SDK.syncData.randomResArr = [...page.randomResArr];
            SDK.bindSyncEvt({
              index: $(".game-container").data("syncactions"),
              eventType: "sendmessage",
              method: "event",
              syncName: "syncInitRule",
            });
          }
        }
      }
    },

    computeResult: function () {
      const gameContainerWidth = $(".game-container").width();
      const gap = gameContainerWidth * 0.05;
      const remInPx = parseFloat(
        getComputedStyle(document.documentElement).fontSize
      );
      for (let i = 0; i < rounds.length; i++) {
        let arr = [];
        while (arr.length < 3) {
          let num = Math.floor(Math.random() * 5);
          if (!arr.includes(num)) {
            arr.push(num);
          }
        }
        page.randomResArr.push(arr);
      }
      console.log("jf result:", page.randomResArr);
    },

    initCommonData: function () {
      if (
        page.randomResArr.length > 0 &&
        (!SDK.syncData.randomResArr || SDK.syncData.randomResArr.length === 0)
      ) {
        SDK.syncData.randomResArr = [...page.randomResArr];
      } else if (
        SDK.syncData.randomResArr &&
        SDK.syncData.randomResArr.length > 0 &&
        page.randomResArr.length === 0
      ) {
        page.randomResArr = [...SDK.syncData.randomResArr];
      }
    },

    gameBegin: function () {
      console.log("gameBegin");
      $(".item-wrap").removeClass("active");
      page.startRound();
    },

    // todo 开始轮次
    startRound: function () {
      console.log("jf  round start");
      // page.clearExperienceValue();
      page.targetsAllowClick = true;
      page.playAudio();
      this.gameState.isPlaying = true;
      $('.content-wrap').attr('data-right',2)
      $('.caidai').hide()
      $('.breath').css({
        display:'block'
      })
      $('.yun').css({
        display:'none'
      })
      page.startCreatEle();
    },

    startCreatEle: function () {
      const { currentRound } = page.gameState;
      const currentRes = page.randomResArr[currentRound];
      const currentConfig = rounds[currentRound];
      const tempMap = [
        currentConfig.target,
        currentConfig.distractorOne,
        currentConfig.distractorTwo,
      ];
      for (let index = 0; index < currentRes.length; index++) {
        const gameItemEleIndex = currentRes[index];

        if (page.breathAnimation[gameItemEleIndex]) {
          lottieAnimations.stop(page.breathAnimation[gameItemEleIndex]);
          lottieAnimations.play(page.breathAnimation[gameItemEleIndex]);
        }
        $(".game-item")
          .eq(gameItemEleIndex)
          .find(".item-wrap")
          .addClass("active");
        $(".game-item")
          .eq(gameItemEleIndex)
          .find(".content-card")
          .attr("data-right",index === 0 ? 1 : 2)
          .css({
            "background-image": `url(${imgsObj[tempMap[index]]})`,
          });
      }
    },

    mountedCreatEle: function () {
      // const { currentRound } = page.gameState;
      // const currentRes = page.randomResArr[currentRound];
      // const currentConfig = rounds[currentRound];
      // const tempMap = [
      //   currentConfig.target,
      //   currentConfig.distractorOne,
      //   currentConfig.distractorTwo,
      // ];
      for (let index = 0; index < 3; index++) {
        // const gameItemEleIndex = currentRes[index];
        
        setTimeout(() => {
          if (page.breathAnimation[index]) {
            lottieAnimations.stop(page.breathAnimation[index]);
            lottieAnimations.play(page.breathAnimation[index]);
          }
        }, 2000);
        $(".game-item")
          .eq(index)
          .find(".item-wrap")
          .addClass("active");
        $(".game-item")
          .eq(index)
          .find(".content-card")
          .attr("data-right",2)
          .css({
            "background-image": `url(${imgs[index].img})`,
          });
      }
    },

    // todo 能量球
    energyBall:function() {

    },

    // todo 处理经验值
    dealExperienceValue: function () {
      const { currentRound } = page.gameState;
      const percent = ((currentRound+1) / rounds.length) * 100;

      const width = `${percent > 100 ? 100 : percent}%`;
      $(".experience-bar-fill").css({
        width,
      });

      page.pauseTargetMusic();
      const audio = $("#experience-audio").get(0);
      audio.currentTime = 0;
      $("#experience-audio").attr(
        "src",
        page.musicConfig.experience
      );
      SDK.playRudio({
        index: audio,
        syncName: $("#experience-audio").attr("data-syncaudio"),
      });
    },

    // todo 清空经验值
    clearExperienceValue: function () {
      $(".experience-bar-fill").css({
        transition: "none",
        width: 0,
      });

      setTimeout(() => {
        $(".experience-bar-fill").css({
          transition: " width 1s ease",
          width: 0,
        });
      }, 500);
    },

    beforeCompleteRound: function () {
      page.gameState.isPlaying = false;
      page.pauseTargetMusic();
      $('.content-wrap').attr('data-right',2)
      $('.item-wrap').removeClass('active')
    },

    // todo 结束本轮
    completeRound: function () {
      setTimeout(() => {
        // $("#reward").hide();
        page.gameState.currentRound++;

        if (page.gameState.currentRound < rounds.length) {
          page.startRound();
        } else {
          // self.showGameComplete();
        }
      }, 1000);
    },

    // todo 游戏结束
    gameOver: function () {
      console.log("游戏结束！");
      // page.gameState.isPlaying = false;
      // page.stopSpawningFruits();
      // page.pauseTargetMusic();
      // $(".fruit").remove();
      page.beforeCompleteRound();
      // todo 播放最后轮动画
      page.execFeedBack();
    },


    playAudio: function () {
      const { currentRound } = page.gameState;
      const audio = $("#effect-audio").get(0);
      audio.currentTime = 0;
      if (page.hornAnimation) {
        lottieAnimations.stop(page.hornAnimation);
        lottieAnimations.play(page.hornAnimation);
      }
      $("#effect-audio").attr("src", audiosObj[rounds[currentRound].audio]);
      SDK.playRudio({
        index: audio,
        syncName: $("#effect-audio").attr("data-syncaudio"),
      });
    },

    pauseTargetMusic: function () {
      const audio = $("#effect-audio").get(0);
      // todo pause target music
      SDK.pauseRudio({
        index: audio,
        syncName: $("#effect-audio").attr("data-syncaudio"),
      });
      audio.currentTime = 0;
    },

    playClickAudio: function (isRight) {
      page.pauseTargetMusic();
      const audio = $("#click-audio").get(0);
      audio.currentTime = 0;
      $("#click-audio").attr(
        "src",
        isRight ? page.musicConfig.right : page.musicConfig.wrong
      );
      SDK.playRudio({
        index: audio,
        syncName: $("#click-audio").attr("data-syncaudio"),
      });

      $("#click-audio")
        .off("ended")
        .one("ended", function () {
          if(isRight) return;
          // todo go on playing target music
          console.log("click-audio ended");
          const audio = $("#effect-audio").get(0);
          audio.currentTime = 0;
          SDK.playRudio({
            index: audio,
            syncName: $("#effect-audio").attr("data-syncaudio"),
          });
        });
    },
    threeTwoOne: function () {
      let q = 1;
      $(".funcMask").show();
      $(".startBtn")
        .hide()
        .siblings(".timeChangeBox")
        .show()
        .find(".numberList");
      SDK.playRudio({
        index: $(".timeLowAudio_" + q).get(0),
        syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
      });
      let audioPlay = setInterval(function () {
        q++;
        if (q > 4) {
          clearInterval(audioPlay);
          SDK.setEventLock();
          $(".funcMask").hide();
          $(".timeChangeBox").hide();
          setTimeout(() => {
            page.gameBegin();
          }, 500);
        } else {
          SDK.playRudio({
            index: $(".timeLowAudio_" + q).get(0),
            syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
          });
          $(".numberList").css({
            "background-position-x": -(1.5 * (q - 1)) + "rem",
          });
        }
      }, 1000); // @WARNING
    },

    createHornAnimation: function () {
      const { horn } = this.defaultConfig;
      getImageSizeFromJSON(horn, async (width, height) => {
        this.hornAnimation = await lottieAnimations.init(
          this.hornAnimation,
          horn,
          ".horn",
          true
        );
      });
    },

    createBreathAnimation: function () {
      const { dishuBreath } = this.defaultConfig;
      const { breath } = dishu;
      const effectJSON = breath || dishuBreath;
      for (let index = 0; index < 5; index++) {
        getImageSizeFromJSON(effectJSON, async (width, height) => {
          $(`.breath`).css({
            width: width / 100 + "rem",
            height: height / 100 + "rem",
          })
          this.breathAnimation[index] = await lottieAnimations.init(
            this.breathAnimation[index],
            effectJSON,
            `.breath-${index}`,
            true
          );
        });
      }
    },

    createYunAnimation: function () {
      const { dishuYun } = this.defaultConfig;
      const { yun } = dishu;
      const effectJSON = yun || dishuYun;
      for (let index = 0; index < 5; index++) {
        getImageSizeFromJSON(effectJSON, async (width, height) => {
          $(`.yun`).css({
            width: width / 100 + "rem",
            height: height / 100 + "rem",
          })
          this.yunAnimation[index] = await lottieAnimations.init(
            this.yunAnimation[index],
            effectJSON,
            `.yun-${index}`,
            false
          );
          this.yunAnimation[index].addEventListener("complete", () => {
            console.log("金币动画结束动画结束")
            lottieAnimations.stop(this.yunAnimation[index]);  
            // // todo 彩带动画
            // if(this.caidaiAnimation[index]) {
            //   $('.game-item').eq(index).find('.caidai').show()
            //   lottieAnimations.play(this.caidaiAnimation[index]);
             
            //   setTimeout(() => {
            //     lottieAnimations.stop(this.caidaiAnimation[index]);
            //     $('.game-item').eq(index).find('.caidai').hide()

            //     const $energyBall = $('.energy-ball')
            //     const $contentEnergy =  $('.game-item').eq(index).find('.content-energy')

            //     const $experience = $('.experience');
            //     // 获取 experience 元素相对于文档的偏移量
            //     const experienceOffset = $experience.offset();
            //     const experienceLeft = experienceOffset.left;
            //     const experienceTop = experienceOffset.top;
            //     console.log('经验条位置:', experienceLeft, experienceTop);

            //     // 获取 contentEnergy 元素相对于文档的偏移量
            //     const contentEnergyOffset = $contentEnergy.offset();
            //     const contentEnergyLeft = contentEnergyOffset.left;
            //     const contentEnergyTop = contentEnergyOffset.top;

            //     $energyBall.css({
            //       left:contentEnergyLeft + 'px',
            //       top:contentEnergyTop + 'px',
            //       display:'block'
            //     })

            //     // 使用 animate 方法进行动画
            //     $energyBall.animate({
            //         left: experienceLeft + 'px',
            //         top: experienceTop + 'px'
            //     }, 1000, 'swing', function() {
            //       $energyBall.hide()
            //       page.dealExperienceValue()
            //     });
            //   }, 1000);
            // }

            if(this.caidaiAnimation[index]) {
              lottieAnimations.stop(this.caidaiAnimation[index]);
            }

            $('.item-wrap').removeClass('active')
            const $energyBall = $('.energy-ball')
              const $contentEnergy =  $('.game-item').eq(index).find('.content-energy')

              const $experience = $('.experience');
              // 获取 experience 元素相对于文档的偏移量
              const experienceOffset = $experience.offset();
              const experienceLeft = experienceOffset.left;
              const experienceTop = experienceOffset.top;
              console.log('经验条位置:', experienceLeft, experienceTop);

              // 获取 contentEnergy 元素相对于文档的偏移量
              const contentEnergyOffset = $contentEnergy.offset();
              const contentEnergyLeft = contentEnergyOffset.left;
              const contentEnergyTop = contentEnergyOffset.top;

              $energyBall.css({
                left:contentEnergyLeft + 'px',
                top:contentEnergyTop + 'px',
                display:'block'
              })

              // 使用 animate 方法进行动画
              $energyBall.animate({
                  left: experienceLeft + 'px',
                  top: experienceTop + 'px'
              }, 500, 'swing', function() {
                $energyBall.hide()
                page.dealExperienceValue()
                
              });
          });
        });
      }
    },

    createCaidaiAnimation: function () {
      const { dishuCaidai } = this.defaultConfig;
      for (let index = 0; index < 5; index++) {
        getImageSizeFromJSON(dishuCaidai, async (width, height) => {
          this.caidaiAnimation[index] = await lottieAnimations.init(
            this.caidaiAnimation[index],
            dishuCaidai,
            `.caidai-${index}`,
            false
          );
        });
      }
    },

    execFeedBack: async function () {
      page.pauseTargetMusic();
      if (page.hornAnimation) {
        lottieAnimations.stop(page.hornAnimation);
      }
      console.log(`执行整体反馈动画！`);
      await feedbackAnimation(`finalFeedback`);
    },
  };
  // page.init();
  page.dealConfigData();

  //断线重连页面恢复
  SDK.recover = function (data) {
    // console.log('-------SDK-RECOVER',data)
    // const { result , currentIndex , betweenStartAndGreat, gameStart, currentTime , currentValue} = data
    // const { content = [] } = configData.carousel
    // console.log('re-data',data,page)
    // page.currentIndex = currentIndex || 0
    // $('.carousel-inner').css('transform', 'translateX(-' + (page.currentIndex * 100) + '%)');
    // $('.carousel-counter').text(`${page.currentIndex + 1}/${content.length}`)
    // if(betweenStartAndGreat) {
    //   $('.start').fadeOut()
    //   const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
    //   if(!isSync || userType === 'tea') {
    //     $('.great').fadeIn()
    //   }
    //   $('.mic-json').slideDown(1000)
    //   page.micJsonAnimation && lottieAnimations.play(page.micJsonAnimation);
    //   console.log('re-netween 执行动画')
    // }else {
    //   $('.start').fadeIn()
    //   $('.great').fadeOut()
    // }
    // page.gameStart = gameStart
    // if(gameStart) {
    //   // $('.dialog').hide()
    //   $(".carousel-control.prev").hide()
    //   $(".carousel-control.next").hide()
    // }else{
    //   $(".carousel-control.prev").show()
    //   $(".carousel-control.next").show()
    // }
    // if(page.currentIndex === 0) {
    //   $(".carousel-control.prev").addClass('not-allowed')
    //   $(".carousel-control.next").removeClass('not-allowed')
    // }else if(page.currentIndex === page.times - 1) {
    //   $(".carousel-control.prev").removeClass('not-allowed')
    //   $(".carousel-control.next").addClass('not-allowed')
    // }else {
    //   $(".carousel-control.prev").removeClass('not-allowed')
    //   $(".carousel-control.next").removeClass('not-allowed')
    // }
    // if(Array.isArray(data.result) && data.result.length > 0) {
    //   page.result = result
    // }
    // if(Array.isArray(data.result) && data.result.length > 0 && data.currentTime !== undefined) {
    //   page.currentTime = currentTime
    //   $('.pannel-wheel').css('transition', 'none')
    //   const targetDegree = result[currentTime - 1]
    //   $('.pannel-wheel').css('transform', `rotate(${targetDegree}deg)`)
    //   setTimeout(() => {
    //     $('.pannel-wheel').css('transition', 'transform 5s ease-in-out')
    //   }, 10);
    // }
    // if(data.currentValue !== undefined) {
    //   page.currentValue = currentValue
    //   const percent = currentValue / page.timesConfig[page.times].total * 100
    //   const width = `${percent > 100 ? 100 : percent}%`
    //   $('.experience-bar-fill').css({
    //     width
    //   })
    // }
    // SDK.setEventLock();
  };

  let gamePauseFlag = false;
  let stuStatus, teaStatus; //检测老师或学生在教室的状态
  // SDK.memberChange = function (message) {
  //   console.log("jf memberChange", message);
  //   if (isSync) {
  //     if (message.state == "enter") {
  //       if (message.role == "tea") {
  //         console.log("jf memberChange tea");
  //         $(".funcMask").hide();
  //       }
  //     }
  //   }
  // };

  SDK.actAuthorize = function (message) {
    console.log("jf actAuthorize", message, isSync);
    if (isSync) {
      // if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
      //   $(".doneTip").removeClass("hide");
      // }

      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        // $(".startBtn").trigger("click");
        // page.threeTwoOne();
        // SDK.setEventLock();

        // if (message && message.type == "practiceStart") {
        //   if (isFirst) {
        //     isFirst = false;
        //     $(".funcMask").show();
        //     page.threeTwoOne();
        //   }
        // }

        const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        if (userType == USER_TYPE.TEA) {
          SDK.setEventLock();
          $(".startBtn").trigger("click");
          SDK.setEventLock();
        }
      }
    }
  };

  // 从JSON文件中获取图片尺寸
  function getImageSizeFromJSON(jsonUrl, callback) {
    $.getJSON(jsonUrl, function (data) {
      const width = data.width || data.w;
      const height = data.height || data.h;
      callback(width, height);
    }).fail(function () {
      console.error("JSON 文件加载失败");
    });
  }

  // 显示图片信息，播放音频
  function getImageSize(url, callback) {
    const img = new Image();
    img.src = url;
    // 确保图片加载完成后获取宽高
    img.onload = function () {
      const width = img.width;
      const height = img.height;
      callback(width, height);
    };
    // 处理加载错误
    img.onerror = function () {
      console.error("图片加载失败");
    };
  }

  /**
   * 授权模式方法
   * @param {*} type: 方法
   * @param {*} value：权限
   * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
   */
  function moduleAuthorizationFn(type, value) {
    if (!isSync) {
      return;
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      // console.log(isSync, classStatus, userType, "292");
      if (classStatus == CLASS_STATUS.NOT) {
        return;
      } else {
        SDK.bindSyncCtrl({
          type: type,
          tplAuthorization: "tpl",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [
              {
                key: "classStatus",
                value: value,
                ownerUID: SDK.getClassConf().user.id,
              },
            ],
          },
        });
      }
    }
  }
});
