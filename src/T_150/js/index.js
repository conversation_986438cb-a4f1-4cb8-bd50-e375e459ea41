"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/template/multyDialog/index.js";
import "../../common/js/teleprompter.js"
import { USER_TYPE, CLASS_STATUS, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";
$(function () {

  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      roundCount:configData.carousel.content.length || 0 ,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.SPEAK
    },
  })
  

  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "3", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  let source = configData.source;
  let classStatus = "0"; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }
  let options = configData.source.options;
  
  if (configData.bg == '') {
    $(".container").css({ 'background-image': 'url(./image/bg.png)' })
  }

  const page = {
    // todo 金币json
    coinJsonAnimation:null,
    // todo 麦克疯json
    micJsonAnimation:null,
    // todo 上管道动画
    pipeTopJsonAnimation:null,
    // todo 下管道动画
    pipeBottomJsonAnimation:null,
    // todo  转盘星星动效
    starsJsonAnimation:null,
    // todo  转盘emoji动效
    emojiJsonAnimation:null,
    // todo 开宝箱特效
    chestJsonAnimation:null,
    // todo 上传的反馈动效
    feedbackJsonAnimation:null,
    // todo 反馈图片
    feedbackImg:null,

    // todo 轮播图当前轮次
    currentIndex:0,

    // todo 游戏是否开始
    gameStart:false,

    // todo 音效配置
    musicConfig: {
      coin:'./audio/coin.mp3',
      pipe:'./audio/pipe-change.mp3',
      stars:'./audio/stars-change.mp3',
      noprize:'./audio/noaward.mp3',
      experience:'./audio/experience-change.mp3',
      feedback:'./audio/feedback02.mp3',
      rotate:'./audio/rotate-change.mp3',
      startAudio:'./audio/startAudio.mp3',
      handleAudio:'./audio/handle.mp3',
    },

    // todo 页面中的默认配置
    defaultConfig: {
      pannelBottomBg:'./image/pannel-bottom-change.png',
      pannelWheelBg:'./image/pannel-wheel-change.png',
      pannelPointerBg:'./image/pannel-pointer.png',
      contentBg:'./image/carousel-change2.png',
      pipeTopJson:'./image/pipe-top-change.json',
      pipeBottomJson:'./image/pipe-bottom-change.json',
      coinJson:'./image/coin-change.json',
      starsJson:'./image/stars-change2.json',
      micJson:"./image/mic.json",
      chestJson:"./image/chest-change2.json",
      feedback:'./image/feedback-change3.json',
      // feedback:'./image/coin-bottom.png'
      handleTop:'./image/handle-top.png',
      emojiJson:'./image/emoji.json'
    },

    // todo 中奖相关配置
    // 轮次配置中奖规则
    timesConfig:{
      '3': {
        total: 150,
        grand: 1, // 大奖
        small: 1, // 小奖
        noAward: 1, // 不中奖
      },
      '4': {
          total: 150,
          grand: 1, // 大奖
          small: 1, // 小奖
          noAward: 2, // 不中奖
      },
      '5': {
          total: 210,
          grand: 1, // 大奖
          small: 2, // 小奖
          noAward: 2, // 不中奖
      },
      '6': {
          total: 210,
          grand: 1, // 大奖
          small: 2, // 小奖
          noAward: 3, // 不中奖
      }
    },
    // 中大奖角度
    grandAwardDegArr:[180,360],
    // 中小奖角度
    smallAwardDegArr:[90,270],
    // 不中奖角度
    noAwardDegArr:[45,135,225,315],
    // 大奖分值
    grandAwardValue:100,
    // 小奖分值
    smallAwardValue:60,
    // 转动次数
    times:configData.carousel.content.length || 0,
    // 当前分值
    currentValue:0,
    // 本次游戏的中奖结果
    result:[],
    // great按钮是否可以点击
    greatBtnAllowClick:true,
    // 当前轮次
    currentTime:0,
    // 把手是否可以点击
    // handleBtnAllowClick:false,

    init: function () {
      console.log('my init re init')
      // todo 初始化syncData
      this.initSyncData();

      // todo 初始化整体页面结构
      this.initPage();

      // todo 初始化中奖规则
      this.initRule();

      // todo 初始化动画
      this.initAnimation()

      // todo 初始化内容轮播图
      this.initCarousel();

    },
    // todo 初始化页面
    initPage: function () {
      const { pannelBottomBg, pannelWheelBg, contentBg, pannelBottonLocationX = 0, pannelBottonLocationY = 0} = configData.pannel
      let effectievPannelBottomBg = pannelBottomBg || this.defaultConfig.pannelBottomBg
      let effectivePannelWheelBg = pannelWheelBg || this.defaultConfig.pannelWheelBg
      let effectiveContentBg = contentBg || this.defaultConfig.contentBg
      getImageSize(effectievPannelBottomBg, (width, height) => {
        $(".pannel-bottom").css({
          "background-image": `url(${effectievPannelBottomBg})`,
          left: pannelBottonLocationX / 100 + "rem",
          top: pannelBottonLocationY / 100 + "rem",
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        });
      })
      
      $(".pannel-wheel").css({
        "background-image": `url(${effectivePannelWheelBg})`,
      });
      $(".pannel-pointer").css({
        "background-image": `url(${this.defaultConfig.pannelPointerBg})`,
      });
      $(".carousel-wrapper").css({
        "background-image": `url(${effectiveContentBg})`,
      });
      

      $('.game-container').on('syncInitRule',function(e) {
  
        console.log('recover ganme-container sdk',page.result,SDK.syncData.result)
        page.result = [...SDK.syncData.result]
        SDK.setEventLock();
      })

      $('.pannel-wheel').on('transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd', function () {
        const targetDegree = page.result[page.currentTime - 1]
        const type = page.getAwardTypeByDegree(targetDegree)
        if(type === 'grand') { 
            // todo 星星炸开动效
            $('.stars-json').show()
            page.starsJsonAnimation && lottieAnimations.play(page.starsJsonAnimation)
            // todo 星星音效
            $('#effect-audio').attr('src', page.musicConfig.stars)
            if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
              SDK.playRudio({
                index: $('#effect-audio').get(0),
                syncName: $('#effect-audio').attr("data-syncaudio"),
              });
            }
        }else if(type === 'small') {
             // todo 星星炸开动效
             $('.stars-json').show()
            page.starsJsonAnimation && lottieAnimations.play(page.starsJsonAnimation)
            // todo 星星音效
            $('#effect-audio').attr('src', page.musicConfig.stars)
            if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
              SDK.playRudio({
                index: $('#effect-audio').get(0),
                syncName: $('#effect-audio').attr("data-syncaudio"),
              });
            }
        }else{
            $('.emoji-json').show()
            page.emojiJsonAnimation && lottieAnimations.play(page.emojiJsonAnimation)
            // todo 播放未中奖音效
            $('#effect-audio').attr('src', page.musicConfig.noprize)
            if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
              SDK.playRudio({
                index: $('#effect-audio').get(0),
                syncName: $('#effect-audio').attr("data-syncaudio"),
              });
            }
            let timer = setTimeout(() => {
              page.carouselAutoNext()
              clearTimeout(timer)
              timer = null
            }, 1000);
        }
        $(this).css('transition', 'none')
        $(this).css('transform', `rotate(${targetDegree}deg)`)

        page.greatBtnAllowClick = true

        setTimeout(() => {
          $(this).css('transition', 'transform 3.5s ease-in-out')
        }, 10);
      })
      $(".start").on("click touchstart",function (e) {
        console.log('start  click')
        if (e.type === "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation();
        const { content = [] } = configData.carousel
        if(content.length < 3) {
          return
        }

        const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
        if(userType === 'stu') {
          return // 学生不允许点击
        }
        if(!isSync) {
          $(this).trigger("sycnStartBtnClick");
          return;
        }
        console.log('sdk start click',SDK,page)

        SDK.reportTrackData({
          action: 'CK_FT_INTERACTION_STARTBUTTON',
          data: {
            roundId: page.currentIndex + 1,
          }
        },USER_TYPE.TEA)

        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          // funcType: 'audio',
          syncName: 'sycnStartBtnClick',
          recoveryMode: '1'
        });
      })
      $(".start").on("sycnStartBtnClick",function (e) {
        console.log('sdk sync start click',SDK,page)
        e.stopPropagation();
        $(".carousel-control.prev").hide()
        $(".carousel-control.next").hide()
        // $('.dialog').hide()
        if(!page.gameStart) {
          page.recoverCarouselStatus()
          if(SDK.syncData.result) {
            page.result = [...SDK.syncData.result]
          }
          console.log('mystart of  result',SDK.syncData)
        }else{
          console.log('mynostart of  result',SDK.syncData)
        }
        $('#effect-audio').attr('src', page.musicConfig.startAudio)
        if ($(window.frameElement).attr("id") === "h5_course_self_frame" ||
        !isSync) {
          SDK.playRudio({
            index: $('#effect-audio').get(0),
            syncName: $('#effect-audio').attr("data-syncaudio"),
          });
        } 
        page.gameStart = true;
        SDK.syncData.gameStart = true;
        $('.mic-json').slideDown(1000)
        page.micJsonAnimation && lottieAnimations.play(page.micJsonAnimation);
        $(this).fadeOut()
        const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
        if(!isSync || userType === 'tea') {
          $('.great').fadeIn()
        }
        SDK.syncData.betweenStartAndGreat = true
        if(page.result.length > 0 && (!SDK.syncData.result || SDK.syncData.result.length === 0)) {
          console.log('sdk start page.result 有数据')
          SDK.syncData.result = [...page.result]
        }else if(SDK.syncData.result && SDK.syncData.result.length > 0 && page.result.length === 0) {
          page.result = [...SDK.syncData.result]
          console.log('sdk start SDK 有数据')
        }
        SDK.setEventLock();
      })
      $(".great").on("click touchstart",function (e) {
        if (e.type === "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation();

        const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
        if(userType === 'stu') {
          return // 学生不允许点击
        }

        if(!isSync) {
          $(this).trigger("sycnGreatBtnClick");
          return;
        }

        SDK.reportTrackData({
          action: 'CK_FT_INTERACTION_SPOKEBUTTON',
          data: {
            roundId: page.currentIndex + 1,
          }
        },USER_TYPE.TEA)

        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'sycnGreatBtnClick',
          // recoveryMode: '1'
        });
      })
      $(".great").on("sycnGreatBtnClick",function (e) {
        console.log('trigger sycnGreatBtnClick' )
        e.stopPropagation();
        // $('.dialog').hide()
        if(!page.greatBtnAllowClick) return SDK.setEventLock();
        // moduleAuthorizationFn("stuOnly", "12")
        // page.handleBtnAllowClick = true
        // $('.handle-top').css('cursor','pointer')
        // $('.handsss').show()
        page.greatBtnAllowClick = false
        $('.great').fadeOut()
        $('.start').fadeOut()
        $('.mic-json').slideUp(1000)
        SDK.syncData.betweenStartAndGreat = false

        page.currentTime++
        SDK.syncData.currentTime = page.currentTime

        page.currentIndex++
        SDK.syncData.currentIndex++

        const targetDegree = page.result[page.currentTime - 1]
        const type = page.getAwardTypeByDegree(targetDegree)
        if( typeof SDK.syncData.currentValue !== 'number' )  {
          SDK.syncData.currentValue = 0
        }
        if(type === 'grand') {
          SDK.syncData.currentValue += page.grandAwardValue
          page.currentValue += page.grandAwardValue
        }else if (type === 'small') {
          page.currentValue += page.smallAwardValue
          SDK.syncData.currentValue += page.smallAwardValue
        }

        if(page.coinJsonAnimation) {
          $(".coin-json").show()
          lottieAnimations.play(page.coinJsonAnimation);
        }

        $('#effect-audio').attr('src', page.musicConfig.coin)
        if ($(window.frameElement).attr("id") === "h5_course_self_frame" ||
          !isSync) {
            SDK.playRudio({
              index: $('#effect-audio').get(0),
              syncName: $('#effect-audio').attr("data-syncaudio"),
            });
        } 
        SDK.setEventLock();
      })

      // $(".handle-top").on("click touchstart",function (e) {
      //   if (e.type === "touchstart") {
      //     e.preventDefault();
      //   }
      //   e.stopPropagation();
      //   if(!page.handleBtnAllowClick) {
      //     return
      //   }

      //   if(!isSync) {
      //     $(this).trigger("syncHandleTopBtnClick");
      //     return;
      //   }

      //   SDK.bindSyncEvt({
      //     index: $(e.currentTarget).data('syncactions'),
      //     eventType: 'click',
      //     method: 'event',
      //     syncName: 'syncHandleTopBtnClick',
      //     // recoveryMode: '1'
      //   });
      // })

      // $('.handle-top').on("syncHandleTopBtnClick",function (e) {
      //   e.stopPropagation();
      //   moduleAuthorizationFn("teaOnly", "11");
      //   $(this).hide()
      //   $('.handle-bottom').show()
      //   //playmusic
      //   $('#effect-audio').attr('src', page.musicConfig.handleAudio)
      //   if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
      //     SDK.playRudio({
      //       index: $('#effect-audio').get(0),
      //       syncName: $('#effect-audio').attr("data-syncaudio"),
      //     });
      //   }

      //   // page.greatBtnAllowClick = false
      //   // $('.great').fadeOut()
      //   // $('.start').fadeOut()
      //   // $('.mic-json').slideUp(1000)
      //   // SDK.syncData.betweenStartAndGreat = false
      //   // page.wheelRotate()

        

        

      //   setTimeout(() => {
      //     $('.handle-top').show().css({cursor:'default'})
      //     $('.handle-bottom').hide()
      //     $('.handsss').hide()
      //     page.handleBtnAllowClick = false


          
      //   }, 500);
      //   SDK.setEventLock();
      // })

      $('.chest').on('animationend webkitAnimationEnd mozAnimationEnd',() => {
        console.log('宝箱抖动动画结束！')

        SDK.reportTrackData({
          action: 'CK_FT_INTERACTION_COMPLETE',
          data: {
            result: 'success',
          }
        },USER_TYPE.TEA)

        $('.mask').fadeIn()
        $('.chest-json').show()
        page.chestJsonAnimation && lottieAnimations.play(page.chestJsonAnimation);
        $('#effect-audio').attr('src', page.musicConfig.feedback)
        if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
            SDK.playRudio({
            index: $('#effect-audio').get(0),
            syncName: $('#effect-audio').attr("data-syncaudio"),
          });
        } 
      },)

      $('.experience-bar-fill').on('transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd',function () {
        const currentValue = page.currentValue
        const totalValue = page.timesConfig[page.times].total
        console.log('经验条动画结束！')
        console.log('当前经验值：',currentValue)
        console.log('总经验值：',totalValue)
        if(currentValue >= totalValue) {
          console.log('经验值大于等于总经验值，开宝箱！')
          $('.chest').css({
            animation:'shake 0.5s'
          })
        }else{
          console.log('经验值小于总经验值，继续游戏！')
          page.carouselAutoNext()
        }
      })
    },

    // todo 初始化syncData
    initSyncData: function () {
    },

    // todo 初始化动画
    initAnimation: function () {
      this.createCoinAnimation();
      this.createMicAnimation();
      this.createPipeTopAnimation();
      this.createPipeBottomAnimation();
      this.createStarsAnimation()
      this.createEmojiAnimation()
      this.createChestAnimation()
      this.createFeedbackAnimation()
    },

    // todo 内容轮播图
    initCarousel: function () {
      let { content = [] } = configData.carousel
      if(!content.length) {
        return
      }
      let innerHtml = ''
      for (let i = 0; i < content.length; i++) {
        const { img } = content[i]
        if(!img) continue;
        innerHtml += `<div class="carousel-item"><img src="${img}" alt=""></div>`
      }
      $(".carousel-inner").append(innerHtml);
      $(".carousel-control.prev").on("click touchstart",function (e) {
        console.log('prev  click')
        if (e.type === "touchstart") {
          e.preventDefault();
        }

        if(page.currentIndex > 0) {
          SDK.syncData.currentIndex = page.currentIndex - 1
          console.log(SDK,'--sdk')
        }

        if(!isSync) {
          $(this).trigger("syncContentPrevBtnClick");
          return;
        }

        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          // funcType: 'audio',
          syncName: 'syncContentPrevBtnClick',
          recoveryMode: '1',
          otherInfor: {
            currentIndex: page.currentIndex - 1,
          }
        });
      })
      $(".carousel-control.prev").on('syncContentPrevBtnClick',function(e,message) {
        console.log('trigger syncContentPrevBtnClick')
        e.stopPropagation();
        console.log(page,message)
        if(page.currentIndex == 0) return SDK.setEventLock();
        page.currentIndex--
        if(isSync) {
          let obj = message.data[0].value.syncAction.otherInfor
          if(message == undefined || message.operate == '1') {
            page.currentIndex = obj.currentIndex
          }
        }
        page.carouselSwitch(page.currentIndex)
        console.log(SDK,'--sdk sync')
        SDK.setEventLock();
      })

      $(".carousel-control.next").removeClass('not-allowed').on("click touchstart",function (e) {
        console.log('next  click')
        if (e.type === "touchstart") {
          e.preventDefault();
        }

        if(page.currentIndex < content.length - 1) {
          SDK.syncData.currentIndex = page.currentIndex + 1
          console.log(SDK,'--sdk')
        }

        if(!isSync) {
          $(this).trigger("syncContentNextBtnClick");
          return;
        }

        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          // funcType: 'audio',
          syncName: 'syncContentNextBtnClick',
          recoveryMode: '1',
          otherInfor: {
            currentIndex: page.currentIndex + 1,
          }
        });
      })
      $(".carousel-control.next").on('syncContentNextBtnClick',function(e,message) {
        console.log('trigger syncContentNextBtnClick')
        e.stopPropagation();
        console.log(page,message)
        if(page.currentIndex == content.length - 1) return SDK.setEventLock();
        page.currentIndex++
        if(isSync) {
          let obj = message.data[0].value.syncAction.otherInfor
          if(message == undefined || message.operate == '1') {
            page.currentIndex = obj.currentIndex
          }
        }
        console.log(SDK,'--sdk sync')
        page.carouselSwitch(page.currentIndex)
        SDK.setEventLock();
      })
      $('.carousel-counter').text(`${page.currentIndex + 1}/${content.length}`)
    },

    // todo 控制轮播图切换
    carouselSwitch: function (index) {
      const { content = [] } = configData.carousel
      $('.carousel-inner').css('transform', 'translateX(-' + (index * 100) + '%)');
      if(page.currentIndex > 0) {
        $(".carousel-control.prev").removeClass('not-allowed')
      }else {
        $(".carousel-control.prev").addClass('not-allowed')
      }
      if(page.currentIndex < content.length - 1) {
        $(".carousel-control.next").removeClass('not-allowed')
      }else {
        $(".carousel-control.next").addClass('not-allowed')
      }
      $('.carousel-counter').text(`${index + 1}/${content.length}`)
    },

    
    recoverCarouselStatus: function () {
      const { content = [] } = configData.carousel
      page.currentIndex = 0
      SDK.syncData.currentIndex = 0
      $('.carousel-inner').css('transition','none')
      $('.carousel-inner').css('transform', 'translateX(0)');
      // $(".carousel-control.prev").addClass('not-allowed')
      // $(".carousel-control.next").removeClass('not-allowed')
      $(".carousel-control.prev").hide()
      $(".carousel-control.next").hide()
      $('.carousel-counter').text(`${page.currentIndex + 1}/${content.length}`)
      setTimeout(() => {
        $('.carousel-inner').css('transition','transform 0.5s ease-in-out')
      }, 0);
    }, 
    
    // todo 转盘停止后自动进入一下轮
    carouselAutoNext: function () {
      console.log('sdk--carouselAutoNext',SDK.syncData)
      this.carouselSwitch(page.currentIndex)
      if(SDK.syncData && SDK.syncData.betweenStartAndGreat) {
        $('.start').fadeOut()
      }else{
        $('.start').fadeIn()
      }
      // $('.start').fadeIn()
      $('.great').fadeOut()
    },

    // todo 金币json
    createCoinAnimation: function () {
      const { coinJson } = configData.pannel
      const effectiveCoinJson = coinJson || this.defaultConfig.coinJson
      getImageSizeFromJSON(effectiveCoinJson, async (width, height) => {  
        this.coinJsonAnimation = await lottieAnimations.init(this.coinJsonAnimation,effectiveCoinJson,".coin-json",false)
        $(`.coin-json`).css({
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        })
        this.coinJsonAnimation.addEventListener("complete", () => {
          console.log("金币动画结束动画结束")
          lottieAnimations.stop(this.coinJsonAnimation);
          $(".coin-json").hide()
          if(this.pipeBottomJsonAnimation) {
            lottieAnimations.play(this.pipeBottomJsonAnimation);
            $('#effect-audio').attr('src', page.musicConfig.pipe)
            if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
              SDK.playRudio({
                index: $('#effect-audio').get(0),
                syncName: $('#effect-audio').attr("data-syncaudio"),
              });
            } 
          }
        });
      })
    },

    // todo 麦克疯动画
    createMicAnimation: function () {
      const micJson = this.defaultConfig.micJson
      getImageSizeFromJSON(micJson, async (width, height) => {  
        this.micJsonAnimation = await lottieAnimations.init(this.micJsonAnimation,micJson,".mic-json")
        $(`.mic-json`).css({
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        })
        this.micJsonAnimation.addEventListener("complete", () => {
          console.log("麦克风动画结束")
        });
      })
    },

    // todo 上管道到动画
    createPipeTopAnimation: function () {
      const { pipeTopJson } = configData.pannel
      let effectivePipeTopJson = pipeTopJson || this.defaultConfig.pipeTopJson
      getImageSizeFromJSON(effectivePipeTopJson, async (width, height) => {  
        this.pipeTopJsonAnimation = await lottieAnimations.init(this.pipeTopJsonAnimation,effectivePipeTopJson,".pipe-top",false)
        $(`.pipe-top`).css({
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        })
        this.pipeTopJsonAnimation.addEventListener("complete", () => {
          lottieAnimations.stop(this.pipeTopJsonAnimation);
          console.log('上管道动画结束')
          // todo 处理经验值
          let timer = setTimeout(() => {
            page.dealExperienceValue()
            clearTimeout(timer)
            timer = null
          }, 500);
        });
      })
    },

    // todo 下管道动画
    createPipeBottomAnimation: function () {
      const { pipeBottomJson } = configData.pannel
      const effectivePipeBottomJson = pipeBottomJson || this.defaultConfig.pipeBottomJson
      getImageSizeFromJSON(effectivePipeBottomJson, async (width, height) => {  
        this.pipeBottomJsonAnimation = await lottieAnimations.init(this.pipeBottomJsonAnimation,effectivePipeBottomJson,".pipe-bottom",false)
        $(`.pipe-bottom`).css({
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        })
        this.pipeBottomJsonAnimation.addEventListener("complete", () => {
          lottieAnimations.stop(this.pipeBottomJsonAnimation);
          page.wheelRotate()
        });
      })
    },

    // todo 转盘星星动效
    createStarsAnimation: function () {
      const starJson = this.defaultConfig.starsJson
      getImageSizeFromJSON(starJson, async (width, height) => {  
        this.starsJsonAnimation = await lottieAnimations.init(this.starsJsonAnimation,starJson,".stars-json",false)
        $(`.stars-json`).css({
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        })
        this.starsJsonAnimation.addEventListener("complete", () => {
          console.log('星星动效结束！')
          lottieAnimations.stop(this.starsJsonAnimation);
          $('.stars-json').hide()
          // todo 播放上管道动画
          this.pipeTopJsonAnimation && lottieAnimations.play(this.pipeTopJsonAnimation)
          // todo 播放管道音效
          setTimeout(() => {
            $('#effect-audio').attr('src', page.musicConfig.pipe)
            if ($(window.frameElement).attr("id") === "h5_course_self_frame" ||
            !isSync) {
                SDK.playRudio({
                  index: $('#effect-audio').get(0),
                  syncName: $('#effect-audio').attr("data-syncaudio"),
                });
            } 
          }, 300)
        });
      })
    },

    // todo 转盘emoji动效
    createEmojiAnimation: function () {
      const emojiJson = this.defaultConfig.emojiJson
      getImageSizeFromJSON(emojiJson, async (width, height) => {  
        this.emojiJsonAnimation = await lottieAnimations.init(this.emojiJsonAnimation,emojiJson,".emoji-json",false)
        $(`.emoji-json`).css({
          width: width / 500 + "rem",
          height: height / 500 + "rem",
        })
        this.emojiJsonAnimation.addEventListener("complete", () => {
          // console.log('emoji动效结束！')
          lottieAnimations.stop(this.emojiJsonAnimation);
          $('.emoji-json').hide()
          // // todo 播放上管道动画
          // this.pipeTopJsonAnimation && lottieAnimations.play(this.pipeTopJsonAnimation)
          // // todo 播放管道音效
          // setTimeout(() => {
          //   $('#effect-audio').attr('src', page.musicConfig.pipe)
          //   if ($(window.frameElement).attr("id") === "h5_course_self_frame" ||
          //   !isSync) {
          //       SDK.playRudio({
          //         index: $('#effect-audio').get(0),
          //         syncName: $('#effect-audio').attr("data-syncaudio"),
          //       });
          //   } 
          // }, 300)
        });
      })
    },

    // todo 开宝箱动效
    createChestAnimation: function () {
      const cheskJson = this.defaultConfig.chestJson
      getImageSizeFromJSON(cheskJson, async (width, height) => {  
        this.chestJsonAnimation = await lottieAnimations.init(this.chestJsonAnimation,cheskJson,".chest-json",false)  
        $(`.chest-json`).css({
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        })
        this.chestJsonAnimation.addEventListener("complete", () => {
          console.log('开宝箱动画结束!')
          lottieAnimations.stop(this.chestJsonAnimation);
          $('.chest-json').hide()
          $('.feedback').fadeIn(2000)
          this.feedbackJsonAnimation && lottieAnimations.play(this.feedbackJsonAnimation)
        });
      })
    },

    // todo 上传的反馈动效
    createFeedbackAnimation: function () {
      const { feedback } = configData
      const effectiveFeedback = feedback || this.defaultConfig.feedback
      const extension = effectiveFeedback.split('.').pop().toLowerCase()
      if (extension === 'json') {
        // todo 上传的是json
        getImageSizeFromJSON(effectiveFeedback, async (width, height) => {  
          this.feedbackJsonAnimation = await lottieAnimations.init(this.feedbackJsonAnimation,effectiveFeedback,".feedback")
          $(`.feedback`).css({
            width: width / 100 + "rem",
            height: height / 100 + "rem",
          })
          this.feedbackJsonAnimation.addEventListener("complete", () => {
            console.log('反馈动画完成')
          });
        })
      }else {
        // todo 上传的是图片
        this.feedbackImg = effectiveFeedback
        getImageSize(this.feedbackImg, (width, height) => {
          $(".feedback").css({
            "background": `url(${this.feedbackImg}) no-repeat center`,
            "background-size": "cover",
            width: width / 100 + "rem",
            height: height / 100 + "rem",
          });
        })
      }
    },

    // todo 处理经验值
    dealExperienceValue: function () {
      // const percent = this.currentValue / this.timesConfig[this.times].total * 100
      const percent = page.currentValue / this.timesConfig[this.times].total * 100
      // todo 播放经验值音效
      setTimeout(() => {
        $('#effect-audio').attr('src', page.musicConfig.experience)
        if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
            SDK.playRudio({
            index: $('#effect-audio').get(0),
            syncName: $('#effect-audio').attr("data-syncaudio"),
          });
        } 
      }, 500);
      const width = `${percent > 100 ? 100 : percent}%`
      $('.experience-bar-fill').css({
        width
      })
    },

    // todo 装盘转动逻辑
    wheelRotate: function () {
      const spinCircles = 7; // 设置转盘多转几圈
      const targetDegree = page.result[page.currentTime - 1]
      const totalRotation = targetDegree + (spinCircles * 360); // 计算总旋转角度
      console.log('recover targetDEgree',targetDegree,page.result,page.currentTime)
      // 设置初始旋转角度，触发过渡动画
      const wheel = $('.pannel-wheel')
      wheel.css('transform', `rotate(${totalRotation}deg)`);
      const { hasRotateMusic = "1" } = configData.pannel
      if(hasRotateMusic == "1") {
        $('#wheel-audio').attr('src', page.musicConfig.rotate)
          if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
            SDK.playRudio({
            index: $('#wheel-audio').get(0),
            syncName: $('#wheel-audio').attr("data-syncaudio"),
          });
        }
      }
    },

    // todo 初始化中奖规则
    initRule: function () {
      // if( SDK.syncData.result && SDK.syncData.result.length !== 0) {
      //   console.log('---------已经有result了',SDK.syncData.result)
      //   page.result = SDK.syncData.result
      // }else{
      //   page.result = this.computedResultByTimes(this.times)
      //   SDK.syncData.result = page.result
      //   console.log('---------初始化result',SDK.syncData.result)
      // }
      // if(!isSync) {
      //   page.result = this.computedResultByTimes(this.times)
      // }else{
      //   page.result = this.computedResultByTimes(this.times)
      // }


      // const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
      // if(!isSync || userType === 'tea') {
      //   // todo 老师进来才会计算结果
      //   page.result = this.computedResultByTimes(this.times)
      //   SDK.syncData.result = [...page.result]
      //   console.log('my 老师进来计算结果',SDK.syncData.result)
      // }

      const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
      if(!isSync) {
        page.result = this.computedResultByTimes(this.times)
        SDK.syncData.result = [...page.result]
      }else {
        if(userType === 'tea') {
          // todo tea coming computed result
          // page.result = this.computedResultByTimes(this.times)
          // SDK.syncData.result = [...page.result]
          // console.log('my 老师进来计算结果 sdk',SDK.syncData.result,page.result)
          // SDK.bindSyncEvt({
          //   index: $('.game-container').data("syncactions"),
          //   eventType: 'sendmessage',
          //   method: 'event',
          //   syncName: 'syncInitRule',
          //   // recoveryMode: '1'
          // });
          
          if(page.result.length === 0 && (!SDK.syncData.result || SDK.syncData.result.length === 0)) {
            console.log('sdk first tea result',SDK,page)
            page.result = this.computedResultByTimes(this.times)
            SDK.syncData.result = [...page.result]
            SDK.bindSyncEvt({
              index: $('.game-container').data("syncactions"),
              eventType: 'sendmessage',
              method: 'event',
              syncName: 'syncInitRule',
              // recoveryMode: '1'
            });
          }
        }
      }
      console.log('page.result', page.result)
    },

    // todo 根据轮次计算中奖结果
    computedResultByTimes: function(times) {
      const config = this.timesConfig[times];
      if(!config) return;
      let { grand, small, noAward } = config;
      noAward--;
      let result = [];// 结果数组
      const firstTimeDeg = this.getRandomElement(this.noAwardDegArr);
      const lastTimeDeg = this.getRandomElement([...this.smallAwardDegArr, ...this.grandAwardDegArr]);
      const lastAwardType = this.getAwardTypeByDegree(lastTimeDeg);
      if(lastAwardType === 'grand') {
          grand--;
      }else{
          small--;
      }
      const tempArr = []
       // 大奖角度
      for (let i = 0; i < grand; i++) {
          // 获取随机角度
          tempArr.push(this.getRandomElement(this.grandAwardDegArr))
      }
      // 小奖角度
      for (let i = 0; i < small; i++) {
          // 获取随机角度
          tempArr.push(this.getRandomElement(this.smallAwardDegArr))
      }
      // 不中奖角度
      for (let i = 0; i < noAward; i++) {
          // 获取随机角度
          tempArr.push(this.getRandomElement(this.noAwardDegArr))
      }
      result = [firstTimeDeg, ...this.shuffleArray(tempArr), lastTimeDeg]
      console.log(`result:${result}`)
      result.forEach(element => {
          console.log(this.getAwardTypeByDegree(element))
      });

      //todo 第一轮必中奖逻辑
      const firstAwardIndex = result.findIndex(item => [...this.smallAwardDegArr, ...this.grandAwardDegArr].indexOf(item) !== -1);
      [result[0],result[firstAwardIndex]] = [result[firstAwardIndex],result[0]]
      console.log(result,'-0-0-0--')
      return result;
    },

    // todo 根据角度来盘端是中大奖，小奖，还是未中奖
    getAwardTypeByDegree: function(degree) {
      if (this.grandAwardDegArr.includes(degree)) {
          return 'grand';
      } else if (this.smallAwardDegArr.includes(degree)) {
          return 'small';
      } else if (this.noAwardDegArr.includes(degree)) {
          return 'no';
      } else {
          return 'error';
      }
    },

    getRandomElement: function(arr) {
      const randomIndex = Math.floor(Math.random() * arr.length);
      return arr[randomIndex];
    },

    // todo 数组乱序
    shuffleArray: function(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    }
  };
  page.init();


   //断线重连页面恢复
  SDK.recover = function (data) {
    console.log('-------SDK-RECOVER',data)
    const { result , currentIndex , betweenStartAndGreat, gameStart, currentTime , currentValue} = data
    const { content = [] } = configData.carousel
    console.log('re-data',data,page)

    page.currentIndex = currentIndex || 0
    $('.carousel-inner').css('transform', 'translateX(-' + (page.currentIndex * 100) + '%)');
    $('.carousel-counter').text(`${page.currentIndex + 1}/${content.length}`)
    if(betweenStartAndGreat) {
      $('.start').fadeOut()
      const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
      if(!isSync || userType === 'tea') {
        $('.great').fadeIn()
      }
      $('.mic-json').slideDown(1000)
      page.micJsonAnimation && lottieAnimations.play(page.micJsonAnimation);
      console.log('re-netween 执行动画')
    }else {
      $('.start').fadeIn()
      $('.great').fadeOut()
    }
    page.gameStart = gameStart
    if(gameStart) {
      // $('.dialog').hide()
      $(".carousel-control.prev").hide()
      $(".carousel-control.next").hide()
    }else{
      $(".carousel-control.prev").show()
      $(".carousel-control.next").show()
    }
    if(page.currentIndex === 0) {
      $(".carousel-control.prev").addClass('not-allowed')
      $(".carousel-control.next").removeClass('not-allowed')
    }else if(page.currentIndex === page.times - 1) {
      $(".carousel-control.prev").removeClass('not-allowed')
      $(".carousel-control.next").addClass('not-allowed')
    }else {
      $(".carousel-control.prev").removeClass('not-allowed')
      $(".carousel-control.next").removeClass('not-allowed')
    }

    if(Array.isArray(data.result) && data.result.length > 0) {
      page.result = result
    } 
    if(Array.isArray(data.result) && data.result.length > 0 && data.currentTime !== undefined) {
      page.currentTime = currentTime
      $('.pannel-wheel').css('transition', 'none')
      const targetDegree = result[currentTime - 1]
      $('.pannel-wheel').css('transform', `rotate(${targetDegree}deg)`)
      setTimeout(() => {
        $('.pannel-wheel').css('transition', 'transform 3.5s ease-in-out')
      }, 10);
    }
    

    if(data.currentValue !== undefined) {
      page.currentValue = currentValue
      const percent = currentValue / page.timesConfig[page.times].total * 100
      const width = `${percent > 100 ? 100 : percent}%`
      $('.experience-bar-fill').css({
        width
      })
    }
  
    SDK.setEventLock();
  }

  let gamePauseFlag = false;
  let stuStatus, teaStatus; //检测老师或学生在教室的状态
  SDK.memberChange = function (message) {
    console.log('sdk mymemberchange',SDK,page)
    try {
      console.log(message)
      // console.log(SDK.syncData.result)
    } catch (error) {
      console.log('message error-----')
      console.log(error)
    }
    // // if (message.role === "stu") {
    // //   stuStatus = message.state;
    // // } 
    // // //上课状态中，学生退出了，游戏暂停
    // // if (classStatus == "1" && stuStatus == "out") { 
    // //   gamePauseFlag = true;
    // // } else { 
    // //   gamePauseFlag = false;
    // // }  

    // if (message.role === "stu") {
    //   stuStatus = message.state;
    // }
    // if (message.role === "tea") {
    //   teaStatus = message.state;
    // }
    // //只要老师没在教室，游戏暂停
    // if (teaStatus == "out") {
    //   gamePauseFlag = true;
    // }
    // //上课状态中，学生退出了，游戏暂停
    // else if (classStatus == "1" && stuStatus == "out") {
    //   gamePauseFlag = true;
    // } else {
    //   gamePauseFlag = false;
    // }
  }

  // 从JSON文件中获取图片尺寸
  function getImageSizeFromJSON(jsonUrl, callback) {
    $.getJSON(jsonUrl, function (data) {
      const width = data.width || data.w;
      const height = data.height || data.h;
      callback(width, height);
    }).fail(function () {
      console.error("JSON 文件加载失败");
    });
  }

  // 显示图片信息，播放音频
  function getImageSize(url, callback) {
    const img = new Image();
    img.src = url;
    // 确保图片加载完成后获取宽高
    img.onload = function () {
      const width = img.width;
      const height = img.height;
      callback(width, height);
    };
    // 处理加载错误
    img.onerror = function () {
      console.error("图片加载失败");
    };
  }

  /**
   * 授权模式方法
   * @param {*} type: 方法
   * @param {*} value：权限
   * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
   */
  // function moduleAuthorizationFn(type, value) {
  //   if (!isSync) {
  //     return;
  //   }
  //   if (isSync) {
  //     const classStatus = SDK.getClassConf().h5Course.classStatus;
  //     // console.log(isSync, classStatus, userType, "292");
  //     if (classStatus == CLASS_STATUS.NOT) {
  //       return;
  //     } else {
  //       SDK.bindSyncCtrl({
  //         type: type,
  //         tplAuthorization: "tpl",
  //         data: {
  //           CID: SDK.getClassConf().course.id + "", //教室id 字符串
  //           operate: "1",
  //           data: [
  //             {
  //               key: "classStatus",
  //               value: value,
  //               ownerUID: SDK.getClassConf().user.id,
  //             },
  //           ],
  //         },
  //       });
  //     }
  //   }
  // }
});
