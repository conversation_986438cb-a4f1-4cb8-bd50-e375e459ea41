<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="SYMS0001FT_声音魔术FT" ; %>
    <%include ./src/common/template/index_head %>
</head>

<body>
  <div class="container" id="container" data-syncresult="1">
    <!-- 学生重连老师弹窗 -->
    <%include ./src/common/template/disconnectRecover/index.ejs %>

      <!-- 反馈动效 -->
      <%include ./src/common/template/feedbackAnimation/index.ejs %>
        <section class="commom">
          <div class="desc"></div>
          <div class="title">
            <h3></h3>
          </div>
        </section>
        <div class="content-main">
          <div class="image-container"></div>
          <div class="speaker-dn">
            <div class="speaker" data-syncactions="speakerBtnClick"></div>
            <div class="speaker-json" id="speaker-json">
              <audio class="speaker-audio" src="" data-syncaudio="speaker-audio"></audio>
            </div>
          </div>

          <div class="record-img"></div>
          <div class="record-btn" data-syncactions="recordBtnClick"></div>

          <div id="microphone" class="microphone"></div>
          <div class="confirm-btn" data-syncactions="confirmBtnClick"></div>

          <div class="explosion" id="explosion"></div>
          <!-- 头像上墙 -->
          <%include ./src/common/template/avatarUpWall/index.ejs %>
        </div>

        <script type="text/javascript">
          document.documentElement.addEventListener(
            "touchstart",
            function (event) {
              if (event.touches.length > 1) {
                event.preventDefault();
              }
            },
            false
          );
          // 禁用手指双击缩放：

          var lastTouchEnd = 0;
          document.documentElement.addEventListener(
            "touchend",
            function (event) {
              var now = Date.now();
              if (now - lastTouchEnd <= 300) {
                event.preventDefault();
              }
              lastTouchEnd = now;
            },
            false
          );
        </script>
  </div>

  <%include ./src/common/template/index_bottom %>
    <%include ./src/common/template/lottie %>
</body>

</html>
