"use strict"
import '../../common/js/common_1v1.js'
import  './animation.js'
import  './drag.js'
import "../../common/js/teleprompter.js"

import {
  showRecord,
  recording,
  hideRecord,
  greatAnimation,
  goodJobAnimation,
  processAnimation,
  waitAnimation,
  playAnimation
} from './lottie_json.js';
import {INTERACTION_TYPE, TEACHER_TYPE, USER_TYPE, USERACTION_TYPE} from "../../common/js/constants";
$(function () {
  //  var script = document.createElement("script"); //创建新的script节点
  // script.setAttribute("type", "text/javascript");
  // script.setAttribute(
  //   "src",
  //   "//cdn.51talk.com/apollo/public/js/vconsole.min.3.3.0.js"
  // );
  // document.body.appendChild(script); //添加到body节点的末尾

  // script.addEventListener(
  //   "load",
  //   function () {
  //     var vConsole = new VConsole();
  //     console.log("vconsole已正常启用");
  //   },
  //   false
  // );
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      roundcount: configData.source.options.length,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.SPEAK
    },
  })


  window.h5Template = {
    hasPractice: '0'
  }
  let h5SyncActions = parent.window.h5SyncActions;
  let options = configData.source.options;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  let source = configData.source
  let index = 0;//第几个目标的下标
  const userType = SDK.getUserType();
  let processStatus = 0; //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
  let duration = 3; //时间
  let timeIntStu = null; //进度条倒计时动画
  let isClick = true;
  if(configData.bg==''){
    $(".container").css({'background-image': 'url(./image/bg_53.jpg)'})
  }
  const page = {
    showOption: function () {
      let html = ''
      for (let i = 0; i < options.length; i++) {
        html += `<li class="pic pic${i}" data-syncactions="pic${i}"></li>`
      }
      $(".mainArea").append('<ul>' + html + '</ul>')
    },
    showAudioArea: function () {
      let html = ''
      for(let i=0;i<options.length;i++){
        if (options[i].audio == '') {
          html += `<li class="listPos">
                      <img class="img" src="${options[i].image}" alt="" data-syncactions="image${i}">
                    </li>`
        } else {
          html += `<li class="listPos">
                      <img class="img" src="${options[i].image}" alt="" data-syncactions="image${i}">
                      <audio class="audioOpt" src="${options[i].audio}" data-syncaudio="audioOpt${i}"></audio>
                    </li>`
        }
      }
      $(".showStartPos").append('<ul>' + html + '</ul>')
      let images=[]
      for(let i=0;i<options.length;i++){
        images.push(options[i].image)
      }

      function firstIndex(arr, text) {
        // 若元素不存在在数组中返回-1
        let firstVal = -1;
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] !== text) {
            firstVal = i;
            return firstVal;
            break;
          }
        }
        return firstVal;
      }
       let firstI= firstIndex(images, "");//console.log("获取某个元素第一次出现在数组的索引",images, firstIndex(images, ''));
      index=firstI
      $(".ans").text("Key: "+options[index].text)
      for(let i=0;i<options.length;i++){
        if(i==firstI){
          $(".listPos").eq(i).css({
            opacity: 1
          })
        }else{
          $(".listPos").eq(i).css({
            opacity: 0
          })
        }
      }
    },
    init: function () {

      $('.hands').css({
        animation:'hand 1s steps(4) infinite',
       ' -webkit-animation':'hand 1s steps(4) infinite'
      })
      this.showOption()
      this.showAudioArea()
      if(isSync){
        if (window.frameElement.getAttribute('user_type') == 'stu') {
          $(".ans").css({
            width:0,
            height:0,
            opacity:0
          })
          $(".hands").css({
            width:0,
            height:0,
            opacity:0
          })
        }
      }
      $(".ans").drag()
      // $(".ans").text("Key: "+options[0].text)
      if(source.handImg!=''){
        $(".handShake").css({
          background: 'url('+source.handImg+')',
         'background-size': '7.6rem 3.2rem'
        })
      }
    }
  }
  page.init()


  //触发魔术事件
  let start =true;

  let chooseObj= document.getElementsByClassName('img');
  let targetObj = document.getElementsByClassName('pic');
  let arrPoint=[0.25,1,0.3,0.2,0.2] ; //抛物线弧度
  let posArr=[] ;     //存储已经抛出去的目标的位置
  let $audioSrc=document.getElementsByClassName("audio")[0];
  $(".btnArea").on('click touchstart', function (e) {
    if (e.type == 'touchstart') {
      e.preventDefault()
    }
    if(index>=options.length){
      return
    }
    if (start) {
      index++
      start = false;
      if (!isSync) {
        $(this).trigger('changePosSync')
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'changePosSync',
          otherInfor: {
            index:index
          },
          recoveryMode: '1'
        });
      }
    }
  });

  $(".btnArea").on('changePosSync', function(e,message) {
    let timestamp = new Date().getTime();
    console.log("changePosSync 获取时间戳",timestamp)
    if (!isSync) {
      index=index
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      index=obj.index
      if (message == undefined || message.operate == 1) {

      } else {
        recover(index)
        SDK.setEventLock()
        return
      }
    }
    if(index==options.length){
      $(".hands").addClass('hide');

    }
    console.log("changePosSync 获取时间戳",timestamp)
    $(".handShake").css({
      animation:'handShakes .6s steps(2) 1',
      ' -webkit-animation':'handShakes .6s steps(2) 1 '
    })
    console.log("changePosSyn 动画获取时间戳 1",new Date().getTime())
    $(".handShake").on('animationend  webkitAnimationEnd', function() {
      console.log("changePosSyn 动画获取时间戳 2",new Date().getTime())
      $(this).css({
        animation:'none',
      ' -webkit-animation':'none '
      })
      console.log("changePosSyn 动画获取时间戳 2======>",new Date().getTime())

    })
    if (index <= options.length){
      console.log("index=====>",index)
         $(".ans").addClass('hide')
         if(index < options.length){
           let textIndex = index
           for (let i = textIndex; i < options.length; i++) {
             if (options[i].image == '') {
               textIndex++
             } else {
               break
             }
           }
           if(textIndex<options.length){
              $(".ans").text("Key: "+options[textIndex].text)
           }
         }

    }
    $(".hands").addClass("hide")

    new Promise((resolve, reject) => {
      setTimeout(() => {
        $(".listPos").eq(index-1).find(".img").css({top:'.78rem'})
        // resolve()
        if (index <= options.length) {
          $audioSrc.currentTime = 0;
          // $audioSrc ? $audioSrc.play() : "";
          SDK.playRudio({
            index: $audioSrc,
            syncName: $(".audio").attr("data-syncaudio")
          })
          resolve()
        }
      }, 1000)
    }).then(() => {
      new Promise((resolve, reject) => {
        setTimeout(()=>{
          let $audioIndex=$(".listPos").eq(index-1).find(".audioOpt").get(0)
          if (index <= options.length&&$audioIndex) {
            $audioIndex.currentTime = 0;
            // $audioIndex ? $audioIndex.play() : "";
            SDK.playRudio({
              index: $audioIndex,
              syncName: $(".listPos").eq(index-1).find(".audioOpt").attr("data-syncaudio")
            })
            resolve()
          }
          $(".listPos").eq(index-1).css({padding:'0',overflow:'visible'})
          let parabola = funParabola(chooseObj[index-1], targetObj[index-1], {}, {
            chooseImg: chooseObj[index-1],
            index: index-1,
            speed: 0.05,
            curvature: arrPoint[index-1]
          }).mark();
          parabola.init();
          for (let i = index; i < options.length; i++) {
            if (options[i].image == '') {
              index++
            } else {
              break
            }
          }
        },1200)
        // resolve()
      })
    }).then(() => {
      new Promise((resolve, reject) => {
        if (index <= options.length) {
          setTimeout(() => {
            $(".listPos").eq(index).css({
              opacity: 1,
              transition: '0.5s'
            })
            SDK.setEventLock()
             if (index <= options.length-1) {
                $(".ans").removeClass('hide')
                $(".hands").removeClass("hide")
             }
              if (userType === 'tea') {
              if (userType !== 'tea') return;
              if (isSync) {
                  SDK.bindSyncEvt({
                      sendUser: '',
                      receiveUser: '',
                      index: $('.startBtn').data("syncactions"),
                      eventType: 'click',
                      method: 'event',
                      syncName: 'startBtnClick',
                      otherInfor: {
                          supporth5audioperation: SDK.getClassConf().course.supporth5audioperation, //老师端是否支持语音播放功能  此信息会传递给学生端
                      },
                      recoveryMode: '2', //不用做断线重连
                  });
              } else {
                $(".startBtn").trigger("startBtnClick");
              }
            }
            // resolve()
          }, 2500)
        }
      })
    })
  });

  //恢复机制
  function recover(index){
    for(let i=0;i<index;i++){
      let parabola = funParabola(chooseObj[i], targetObj[i], {}, {
        chooseImg: chooseObj[i],
        index: i,
        speed: 10,
        curvature: arrPoint[i]
      }).mark();
      parabola.init();
      if(options[i].image!=''){
        $(".listPos").eq(i).css({padding:'0',overflow:'visible',opacity:1})
      }
    }
    $(".listPos").eq(index).css({
      opacity: 1
    })
    if(index>=options.length){
      $(".hands").addClass('hide')
    }
  }


  // 小话筒
     /**
     * 动画部分
     */
    // 举起麦克风
    var showRecordInit = lottie.loadAnimation({
      container: document.getElementById("showRecord"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: showRecord(), //如果使用的是JSON
  });
  // 话筒两侧线条跳动
  var recordingInit = lottie.loadAnimation({
      container: document.getElementById("recording"), // 容器
      renderer: "svg",
      loop: true,
      autoplay: false,
      animationData: recording(), //如果使用的是JSON
  });
  // 录音进度条
  var processInit = lottie.loadAnimation({
      container: document.getElementById("processAni"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: processAnimation(), //如果使用的是JSON
  });
  // 放下麦克风
  var hideRecordInit = lottie.loadAnimation({
      container: document.getElementById("hideRecord"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: hideRecord(), //如果使用的是JSON
  });
  // 等待中动画  等待打分结果时使用
  var waitInit = lottie.loadAnimation({
      container: document.getElementById("wait"), // 容器
      renderer: "svg",
      loop: true,
      autoplay: false,
      animationData: waitAnimation(), //如果使用的是JSON
  });
  // 播放动画
  var playInit = lottie.loadAnimation({
      container: document.getElementById("play-record"), // 容器
      renderer: "svg",
      loop: true,
      autoplay: false,
      animationData: playAnimation(), //如果使用的是JSON
  });
  var greatInit = lottie.loadAnimation({
      container: document.getElementById("great"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: greatAnimation(), //如果使用的是JSON
  });
  var goodJobInit = lottie.loadAnimation({
      container: document.getElementById("goodJob"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: goodJobAnimation(), //如果使用的是JSON
  });
  // 点击开始录音按钮
//   $(".startBtn").on("click touchstart", function(e) {

// })
  $(".startBtn").on('startBtnClick', function(e, message) {

        $(".startBtn").hide();

        startRecord(); //开始录音动画(录音功能有一定时间延迟，因此动画较晚)
        SDK.setEventLock();
    })
    // 举起麦克风
    function startRecord() {
      if (userType === 'tea') {
          $(".record-con-tea").show(); // 老师端显示"recording Stu‘s voice" 学生录音中
      }
      $("#showRecord").show();
      showRecordInit.play();
      SDK.playRudio({
          index: $('#startAudio').get(0),
          syncName: $('#startAudio').attr("data-syncaudio")
      })
      showRecordInit.addEventListener('complete', function() {
          setTimeout(function() {
              $("#showRecord").hide();
              $("#recording").show();
              showRecordInit.stop();
              recordingInit.play();
              processStart();
          }, 500)
      });
      console.log("提前绑定放下麦克风动画的事件")
      hideRecordListener();//提前绑定放下麦克风动画的事件
  }
  //进度条动画
  function processStart() {
    processStatus = 1;
    $("#processAni").show();
    processInit.setSpeed(1)
    if (duration > 20) {
        processInit.setSpeed(4.5 / duration)
    } else {
        if (duration < 5) {
            processInit.setSpeed(6 / duration)
        } else {
            processInit.setSpeed(5 / duration)
        }
    }
    processInit.play(); //开始进度条动画
    processEnd() //录音倒计时
}
/**
     * 录音倒计时
     */
function processEnd() {
  if (timeIntStu) {
      clearTimeout(timeIntStu);
      timeIntStu = null;
  }
  timeIntStu = setTimeout(function() {
      if (processStatus != 2 && processStatus != 3) {
          console.log("processStatus", processStatus)
          finishRecord(); //结束进度条 放下麦克风
      }
  }, duration * 1000);
};

// 结束进度条 放下麦克风
function finishRecord() {
  processInit.stop();
  $("#recording").hide();
  console.log("进度条结束，准备放下麦克风")
  $("#hideRecord").show();
  hideRecordInit.play();
  $("#processAni").fadeOut(100);
  processStatus = 2; //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
}
//放下麦克风 监听事件。
function hideRecordListener(){
  console.log("放下麦克风 已绑定事件")
  hideRecordInit.addEventListener('complete', function() {
      console.log("麦克风已放下")
      //移除进度条，播放退出动画
      processStatus = 3;
      $("#hideRecord").hide();
      $(".record-con-tea").hide();
      hideRecordInit.stop();

      if (userType === 'stu') {
        $("#wait-container").show();
        // waitInit.play(); //显示等待中动画
        showEvaluate(); //告知老师需要显示评价
        $("#wait-container").remove();
        // waitInit.stop();
      }else {
        showEvaluate(); //告知老师需要显示评价
      }
  })
}

//开始播放动画
$("#playSyncbtn").on('playSyncName', function(e, message) {
  console.log("开始播放动画")
  $("#play-record-container").show();
  playInit.play();
  SDK.setEventLock();
})

//学生已收到结束播放通知，结束播放动画  同时告知老师结束播放动画（只有学生可收到）
function stopBySDK() {
  SDK.bindSyncEvt({
      sendUser: '',
      receiveUser: '',
      index: 'stopPlaySync',
      eventType: 'click',
      method: 'event',
      syncName: 'stopPlaySyncName',
      recoveryMode: '2', //不用做断线重连
  });
}

//结束播放动画 (学生是直接调用 老师是收到通知调用)
$("#stopPlaySyncbtn").on('stopPlaySyncName', function(e, message) {
  console.log("结束播放动画")
  playInit.stop();
  $("#play-record-container").hide();
  SDK.setEventLock();
  showEvaluate(); //告知老师需要显示评价
})
//告知老师显示评价
function showEvaluate() {
  console.log("告知老师显示评价")
  if (!isSync) {
    $(".evaluateTip").show();
  }else {
    SDK.bindSyncEvt({
      sendUser: '',
      receiveUser: '',
      index: 'showEvaluateSync',
      eventType: 'click',
      method: 'event',
      syncName: 'showEvaluateSyncName',
      recoveryMode: '2', //不用做断线重连
  });
  }

}
 // 老师显示评价
 $("#showEvaluateSyncbtn").on('showEvaluateSyncName', function(e, message) {
  SDK.setEventLock();
  if (processStatus != 6) {
      if (userType !== 'tea') return;
      processStatus = 6
      console.log("evaluateTip")
      $(".evaluateTip").show();
  }
})
// 点击great按钮
$(".great-btn").on("click touchstart", function(e) {
  if (e.type == "touchstart") {
      e.preventDefault()
  }
  e.stopPropagation();
  $(".evaluateBtn").addClass("disableBtn").attr('disabled', 'disabled');
  $(".evaluateTip").hide();
  console.log("great-btn 点击1")
  if (isSync) {
      SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'greatBtnClick',
          recoveryMode: '2', //不用做断线重连
      });
  } else {
      $(this).trigger("greatBtnClick");
  }
  SDK.reportTrackData({
    action:'CK_FT_INTERACTION_SPOKEBUTTON', //事件名称
    data:{}, // 老师和学生  都需要上报的数据
    teaData:{
      roundid:index
    },  // 只有老师端会上报的数据
    stuData:{},  // 只有学生端会上报的数据
  },USER_TYPE.TEA)
  if (index==options.length){
    SDK.reportTrackData({
      action:'CK_FT_INTERACTION_COMPLETE', //事件名称
      data:{}, // 老师和学生  都需要上报的数据
      teaData:{
        result: 'success'
      },  // 只有老师端会上报的数据
      stuData:{},  // 只有学生端会上报的数据
    },USER_TYPE.TEA)
  }
})
$(".great-btn").on('greatBtnClick', function(e, message) {
  console.log("great-btn 点击2")
  greatAni();
  SDK.setEventLock();
  // $('.hand').eq(clickCardNum).css('opacity','1');
})
// 点击goodjob按钮
$(".goodjob-btn").on("click touchstart", function(e) {
  if (e.type == "touchstart") {
      e.preventDefault()
  }
  e.stopPropagation();
  $(".evaluateBtn").addClass("disableBtn").attr('disabled', 'disabled');
  $(".evaluateTip").hide();
  if (isSync) {
      SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'goodJobBtnClick',
          recoveryMode: '2', //不用做断线重连
      });
  } else {
      $(this).trigger("goodJobBtnClick");
  }
  SDK.reportTrackData({
    action:'CK_FT_INTERACTION_SPOKEBUTTON', //事件名称
    data:{}, // 老师和学生  都需要上报的数据
    teaData:{
      roundid:index
    },  // 只有老师端会上报的数据
    stuData:{},  // 只有学生端会上报的数据
  },USER_TYPE.TEA)
  if (index==options.length){
    SDK.reportTrackData({
      action:'CK_FT_INTERACTION_COMPLETE', //事件名称
      data:{}, // 老师和学生  都需要上报的数据
      teaData:{
        result: 'success'
      },  // 只有老师端会上报的数据
      stuData:{},  // 只有学生端会上报的数据
    },USER_TYPE.TEA)
  }
})
$(".goodjob-btn").on('goodJobBtnClick', function(e, message) {
  goodJobAni();
  SDK.setEventLock();
  // $('.hand').eq(clickCardNum).css('opacity','1');
})
//great动画
function greatAni() {
  console.log("great-btn 点击3")
  var timeIntFinish = setInterval(function() {
    console.log("great-btn 点击44")
      $("#great").show();
      SDK.playRudio({
          index: $('#greatAudio').get(0),
          syncName: $('#greatAudio').attr("data-syncaudio")
      })
      greatInit.play();
      console.log("great-btn 点击开始播放")
      greatInit.addEventListener('complete', function() {
          greatInit.stop();
          $("#great").hide();
          $(".evaluateBtn").removeClass("disableBtn").removeAttr('disabled');
          start = true;
          processStatus = 6
      });
      clearInterval(timeIntFinish);
      timeIntFinish = null;
  }, 500)
}

//goodJob动画
function goodJobAni() {
  var timeIntFinish = setInterval(function() {
      $("#goodJob").show();
      SDK.playRudio({
          index: $('#goodJobAudio').get(0),
          syncName: $('#goodJobAudio').attr("data-syncaudio")
      })
      goodJobInit.play();
      goodJobInit.addEventListener('complete', function() {
          goodJobInit.stop();
          $("#goodJob").hide();
          $(".evaluateBtn").removeClass("disableBtn").removeAttr('disabled');
          start = true;
          processStatus = 6
      });
      clearInterval(timeIntFinish);
      timeIntFinish = null;
  }, 500)
}


})



