{"v": "5.9.6", "fr": 26, "ip": 0, "op": 45, "w": 216, "h": 216, "nm": "音量216-216", "ddd": 0, "assets": [{"id": "image_0", "w": 42, "h": 31, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAfCAYAAACRdF9FAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAACB0lEQVRYhc2XP3ITMRSHv7cwQ2UqKqioCAcgB8A5QHKAmAPE6TEUdPEFyAFi+sQHSOiJ+0zc0MUV3XY0P4qVM/ai/SNZa+eb8XhsP8nf6OlJb+GJIKkv6VrSnXv9kjSS1AOwXQsCSBoBxxU/z4HBsy36eJG0D3yrCXkF/H3eocAboA/0gH3gAZia2W0ptN9iuqPkqZe0BwyAw4qQqZl9WYm/AD40zZtsRZ3gqMWfHkrKzWzsPudt5s82kQOQ1JN0BlzSYmUcx8tqBspbwcfPjUQlDYBrqtNcx3v3fgUsGmInUaJuFS+AzxTFEo2Z5cAQv2wOfDWz2+A9KqkPnG0quIqZ3QMHko6APTf3PXBjZg8QeOBLOgFOErgtzOwgZEDrFXUFE7MXfYybQ9ZpXFFXnRPgXYxRiQUwNrOb0IG1ogklZ8AkRnBJpWgiyRlw7rk2g/GKJpCMTnEVVcU0Jl7yB/DdnY/J+E/UVffHiLly4DRFmn2siboDN+YImgPD5eHcBY971HU/lxFzTCn2Y9JUlzF4LJ4r4HXg+LXeskuWTcmQJywJkLlnlqoHqyq2KgnFioY2GbNtSwKYpLuA+Dkw6LpwfIQ0zjkw2oUkhImeuwZ3J2QUjUMTMzObdC1TR0Zxr9elMwe2XjxlMpfOU/yyC+BTl1djW1av0BfAW+Cl++qPmf3eiZWHf41twSDhdKkdAAAAAElFTkSuQmCC", "e": 1}, {"id": "image_1", "w": 64, "h": 79, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABPCAYAAABbP8MLAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAADU0lEQVR4nO2cUVLaUBSG/3NxfC07KIzBV9mBx66AR6XONK6guAO6A3ZQnNHgo90Aue6AvlY66A70Ucbk9AHiCAU6lJCTmHxPQCD3vx/nJrmZJIR3xMjlcvAMFjJ1ADCEx0BCu+/ZwbLfUHLxtsfI5fLL2LQI0gLwYcFXHgBpO57tzi/IvIBhk12AOljc8VkEF07Pd99+lFkBv5pcN5OOH671wzkJmRMwcrkcjE0bkK//uw4iOt+76neAjAlYq9xX81TalUq1ax93Ysi1de5OmSmkNtYt9+V8CMZoAOimWsBkt0YdhPgS+8qFGEDXxL7imPj9+VMrGNM9aAudBwBCBQBSVwF3p8wIqCMiB1tu6hBIkYCZck9w05yKIbD1cl+BagUkWO5LURGgVe6LSFRANGkJxtICbXwwEwsrBYyOuRKWTENEGEB5k4ZEUA7GOCDIJquJkydgiYDRMVcCQ90AOITEE5jSd9A9ABYIGDbZDUDfk8+jw8xucDrZyE3ngTcChifcyFvngamAkctlEHWVs6hgAOBlbJadS3v3mJHL5enJxFxigmcwcvrvA4ABGdYOoYkBpK4dQpNUTIc1KQRoB9CmEKAdQJtCgHYAbQoB2gG0KQRoB9CmEKAdQJtCgHYAbQoB2gG0KQRoB9CmEKAdQJtCgHYAbQoB2gG0KQRoB9CmECAgqx1CE0MSLr2lLA8Yp2dvADxoB9HCAIAgnxdIAVMBNa/fFsFP7TAavO4FhMTF9PrZPPEqYN+zA0j+JMwcBzg9exNCGDnaKP51ILTv2UFpV+oC+oYcVMM/L2K/O2VGaHjzhoQR342PcXDreD4nfhX/8IQbIOoA+Jh023PcOp7Pic8FnJ69cTy/kpYhpjYZqnn9dimUOoAfWhkA5dlg9dreO57fECNHUNrzpGI6XLu01vH8ChGdI+FhkQoBEXtX/U5pVyoQXCTVZqoEAEC1ax+dnu+KkaMk5iepExBRu7S21vPr2xoW0Ymg1AqI2NawEIQ3QMaeIRLdbE2ETW+2vnU8n4EMVMBb4hoWYqQdvc6UgIjNhoWc1S6tjd5laggsYr0HKsnZ/OO0Mi8gYthkV4RaC7cPgouSSLt6be/nF70bARGjY6687EyeEANMthurvv8Huhsgf7sW0KEAAAAASUVORK5CYII=", "e": 1}, {"id": "image_2", "w": 1, "h": 1, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC", "e": 1}, {"id": "image_3", "w": 184, "h": 164, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALgAAACkCAYAAAAkJ+QnAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAANaUlEQVR4nO2dvU4cWRaAv0JI0wGSeyUHBJbcwQQORoJggs2m32A6ZSZw+wmGfQK3n2B5A9qBTbjwBNtkG6y0IE3gwAFIDggIaKmDClrUBveWjRgD99yf+j2fVMIa3foZ+Pr0uef+VIYSRHHIkAG7FAzJ2LX/eRcYCi91YQ+ABUC2Z34q/mR1P0CbKI4YU7BrRR4Bv1Rw2yVwRsEZGWfccpb9zlkF9+0EKvgDFIcM+YExMCZjDOzU+0R/4RQT6Rca6R9GBb9D8YFdNpgAE5on9FOcAMcY4S9qfpbG0HvBiw/skjElYwK8rPt5InFOwZyM477L3kvBbfoxJWNK+yK1lBPgONtjXveD1EGvBC+OGANT4HW9T1ILSxvVD/oU1XsheHHEFCN2FVWPNnACHPShc9ppwa3YM7qTW8fmFJh1WfROCq5ii+ms6J0S3ObYMzQV8eUE2O9Sjt4JwYsjRsAB8GttDzG893NgDxdWwNoeqzs/6+MdOQfZG25qfYoItF7w4ogZsA88q+SGA2DLHkNkIkvJMaKvgBu+yV8NlxTsZ79xXNkdE9Bawe2o45zUdexN4DlG5lLoOillL4/0wp+QM21rNG+l4DZqv012gwFG6m1MpG4y13eOdLIvKZi2MZq3SnCba89J0YncxAjdBqkfIr3s78nZb1M0b43gxUcmZMyJnWsP+SZ2V1hjJP9Cis7qJbdM2jJltxWCFx85IOOPqBfdBl7Q3mjtyg1mGUXcmLvElBPnUa+agEYLblfLHBMzJdnGLFWou7NYNTlG9KuoV32f7TGNesXINFbw6FWSvop9nxz4RMyIfkrOpKl5eSMFt3IviJFvD4Ef6X4qIuUG+EysHP0cmDRxBLRxgtt5JAeEyj3AiP08/Jk6zRdM6hJedVlyy7hpnc9GCW7lPgy+0AtMOrIZfKV+sMakLdfBV2qc5I0RPIrcA+AV8g0bFMM1RvSwaN4oyRsheBS5tzEpiUbtMNbAn4R2Qhsjee2CB8u9iRG7SwM1TeACAruMjZC8VsGD5R4AP6EVklTcYKK5f8pSu+S1CR5cChxi5NaUJC05RnL/cuIlObt11clrETxY7m1MZ1KphvC8/JyccR2Sb1R9w+KQoR2h9JN7hMpdNZuY7UT9+zk7DDiI9jwCKhfczi3xG35/hRFcqYdXhEj+2s7jr5RKBbf/g34Tp8J+uUoswv4Ob4uPTOI9zNNUloPb+dz/8jpZ5W4en/CdmbgEdquat1JJBC8OGdrFCnJU7mbi/3d5BtUtfasmRTF5t7xTOULlbjL+ku9UlY8nF7z4yD4+eXc5f1tpNq/wHWh7azdqSkrSHNwuEj5DGr2H8PVtN0rzWWP+yvLBoOSDQKkj+Byp3OXwu9IeNjGRXD6q/JIf0qYqyQT3Sk020eH3trKF3wBcxh/Fh3Tf10kEt1WTmfhEXVrWbp5jFptI2Ui3Oj9NBB94LDnr2t4kfcUvSO3YmaXRid7JtBOp/ic6aQD8jKYmXWGF6XTKptkuyRnF7nDGj+AbHpNq/DooSlPZwqfE+4wB+7EfJWoEt3XNf4tOeoH5WlO6xxnSKbbRh/FjR/CZqPUmOpjTZeSB6xlSh54gmuC2kyArC2pq0m38UpXXdoAwCjEj+FTUeohuytMHXuCzXd4s1u2j5OBeuffPaM27L1xhptdKyPlbjIpKrAg+E7Vu8ybzihyfv3ekikqw4DZfkuXeo9C7Kq1D3uFshuD4RO++b2HcR8qXeLnzLMboZpDgxSFDEK6xG4XcUWk18nkqwVE8LIIPmCCZc6LRu988R/r33wktGYamKFNRa51MpYyE7YuwKO4tuLhzWb4ZWOk328gG97KwbSb8I3ghvLHPPGGlm8i+yV+G7KXiL3gmSE/K12ErCsiDXUAU9xLcpifu2689R+ecKN8YIB34Gfveyi+CS9MT7Vwq95FF8Ze+6zb9BM8En6gB2rlU/oo0Zd3wS1N8c/BfnVtq7q18D3m/rBrBxbsRafRWHkLmxo4dORfhE8HHzi21eqI8htSNH+SdzbSCa/RWHmOAdOh+LL2Fj+DuvVkVXHkKSRTP5JUUkeC2VOM+uUoFV55C5oh4l2JZBN8QfII20VU7ytMIHZHWw6Upysi5pcqtuCDNwyVBFrngY+eWmp4orsiC4UjSWCO4Uj8J56VIBX/p3FJX7iiuyAQX5QbOgosnu2gEV1wRLmOTNHaP4BuCT45Gb0WCtJIiGLKXpCgj55YquCJFsl5g4J5NqOBKM0iU0qZ6hYmipGTk2jBNBFcUKYlq4WkE1wqKIiXRmt00KYouMFYaQvJ31StKnajgSqdRwZU2MnZtqIIrbcT51SYquNJGzlwbquBKp1HBlU6TRvBVkqsqihh3wQv3vIe1z6MovSZPc1l3wbPwl3IqyoPIBL9wbagpitJGLlwbaoqiNINE+UGaFEUjuCJBGBCzPRaubd0FzzWCK4lIGBCdBc/eCL9EtEuquCLrYJ5KGks7me4XT1T2UTqILIKLQqdUcM3DlfjIXHFPlZEL7n5xFVxxRRKTJdU85IIvnFtqDq64IA2EWUrBJZUUUMmVp5E5ssz23Ad5QCi4raRcOp+ggitPIXNEFmDxG6pfOLdUwZWnkDmykF4+veA66KM8xAqpHwvpLdIKDnDtcQelH1zJmkuG6EvEgtsk3z0PV8GVh5C5IRrBLPGdLrtwbqlpivI9VshGuwuOfW7jJ7jkZms0iit/RZiekFUoePYbx8DS+QQVXLmPzIlLaf27JGRFj/sn6hqdfKV844ZK0hMIEVx6U+lXktJdpC4UzH1vlfmeCFAccYPru+sHwN9D7qZ0ghz4j+iMy2zP/+ULoYuO3aN4jkZxxSd6H4TcLkzwW+HNvwTdTWk7a+QOeFZPSoIEz37nDMmgzwqdn9JnrpCOiZz4Vk9KYuyLMhO1vohwR6WdSKN3QOeyJFzwXFgTv0GjeB/5grRUfGnHW4IIFtzOEZfl4p9D76q0ijU+39yzGLeOtXXbXNR6hVZU+sQXpLn30mYGwUQR3HYE3otOukAnYfWBHJ/ofSDeh+cBYm6+ORO1ztGyYR+Qp6NL8rDa912iCe4dxXV7ie5yjc9Eu2jRG+JvnzwTn/Ep8hMozWBN7dEbIgtuo/g70UkrtDbeRS7wmUG6HzN6Q4oN8M0n0L0uDpqqdI1rfPpXl9le+MDOfaILbj+B++ITP6FVlS6wxjftnEZ9DkuSV5jYT6JskegKHQDqAn/iE6hOfFbMu5DuPZm3HlH8Ch0AajOf8ZmGscTnG9+RZILbmYayDieYrzfNx9vHFX7jGgWz0BmDjxG0oseF4ogzYEd00iZm9c9miidSorPC7BooT01Osz3GsR/nLulf5X3r0XlY4/sLU6omx/dvtSRRx/IuyQX3TlXKqKA0lzW+nUqA/ZSpSUnyFKWkOGIB/CI+cRt4FftplGDKb1m//tL7bC999IYqUpSSnAnSASAwnRcdzm8WYXKfk6ermtynMsHtANDE62SVvDmEyb3klmns4fjHqC6CY7e/LfiH18ml5NrxrI8wuaFgavtklVGp4ADZbxwgnVZbcoVWV+qi7PT7j1G8i7HGUkplncz7eHc6AbaAnzC7ZSnp8a9zl1TWqbxP5RH8K6bTee517gr4LzriWQVXmN+1v9yndckNNQqeveGGnDG+kq8xv3hd9paOz4R27s9tIKuN2lKUkuIDu2ywwHUTz+/xHFMr16H9OOSYAZywb8hzcsZVVky+R+2CQyTJB5i8fCvSQ/WVa2JUqxohNzREcIgkOcDIHoqMcqFC+Ns4GiM3NEhw+Cr5HOnsw/tsAT8CwwgP1QfiRG1omNzQMMEBikOGDFgQKjmYeSw/orn5Q+QYsePoeErOpElyQwMFh6+SH+NbJ7/LJvACTVvuUu4VGK8CVVud+ykaKXhJccQceB3lYgOM5NtRrtZOyg3o5XsFPsa7bC/ORpkpaLTgAMURU+Aw2gX7KHoasZcUTOsYfpfQeMEhYoXlLgOM5C/obo5e7v8of7PCU5xzW/3EKR9aIThEzsvvs22PrlRdrjFSp3kB73vy+DtQpaI1gpcUH9kn459JLl5G9W3aN5Gr3HM9frQuWWKWmc2TXD0RrRMcItbLH2MLE9G3ae7o6DWmxJf+TdKnwLSKNZSxaaXgJcURM+Bt8httYmQvj7qEL99vtCJV+nGfJQUzO4e/lbRacIDiiBHmFSrxc/PHKEUf2J9bxOus5vZY3Tuq5YSKVr6npPWCl9hy4gx4WeuDDPiWv7tIv+abvKXY9XKJSUcWdT9IDDojOHyttOxj9rqLV1LsB63sRD5FpwQvUdFFLIED8rivDmkKnRS8REV/lE6LXdJpwUvuiD6l7hy9fi6BGTnHXRa7pBeC36X4yISMfaquutTPCQXzps8diU3vBC+x5cUp3Y7qlxQckHHc9nKfL70V/C7FB3bJmJIxof2yX1JwTMG8DZOhUqOC38PKPraytyWNOaFg0edI/RAq+BMUR4zh67FL/dWYJbDA7DW16MqATCpUcCE2dx9hhC//nUL8JUbiC3ssgAuN0DJU8IjYWY7lrPKx+4nckNl8+ZYbzZ3j8X8NiH2u/qO91gAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_4", "w": 217, "h": 217, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "comp_0", "nm": "预合成 1", "fr": 25, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.8, "s": [100]}, {"t": 41.6, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10.4, "s": [546.449, 541.125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 41.6, "s": [593.449, 541.125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.449, 1.125, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [21, -22]], "o": [[0, 0], [-23, 23.75]], "v": [[36, -20.5], [35.75, 22.75]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.862745157878, 0.352941176471, 0.254901960784, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10.4, "s": [50]}, {"t": 31.2, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10.4, "s": [50]}, {"t": 31.2, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 12.48, "op": 792.48, "st": 12.48, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.397], "y": [1]}, "o": {"x": [0.603], "y": [0]}, "t": 15.6, "s": [100]}, {"t": 34.32, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.491, "y": 1}, "o": {"x": 0.509, "y": 0}, "t": 5.2, "s": [546.449, 541.125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 34.32, "s": [598.449, 541.125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.449, 1.125, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [21, -22]], "o": [[0, 0], [-23, 23.75]], "v": [[36, -20.5], [35.75, 22.75]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.862745157878, 0.352941176471, 0.254901960784, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.413], "y": [1]}, "o": {"x": [0.587], "y": [0]}, "t": 5.2, "s": [50]}, {"t": 23.92, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.413], "y": [1]}, "o": {"x": [0.587], "y": [0]}, "t": 5.2, "s": [50]}, {"t": 23.92, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 6.24, "op": 786.24, "st": 6.24, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "Layer 6", "parent": 8, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [61.416, 34.891, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [20.959, 15.203, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 780, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "Layer 7", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [524.454, 541.269, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [31.507, 39.205, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.133, 0.133, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.486, 0.486, 0.333], "y": [-0.005, -0.005, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.547, 0.547, 0.667], "y": [1.011, 1.011, 1]}, "o": {"x": [0.885, 0.885, 0.333], "y": [0, 0, 0]}, "t": 17.68, "s": [90, 90, 100]}, {"t": 36.4, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 780, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "Layer 8", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [432, 648, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 780, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "Layer 9", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [91.916, 81.851, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 780, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "图层 1", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [108.177, 108.176, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 780, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "Layer 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.133], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.885], "y": [0]}, "t": 14.56, "s": [-15]}, {"t": 27.04, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [91.916, 81.851, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 780, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "预合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [107, 107, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1080, "h": 1080, "ip": 0, "op": 780, "st": 0, "bm": 0}], "markers": []}