/****
 * 批量编译打包模板工具
 */

const spawn = require('child_process').spawn;
const path = require("path");
const fs = require("fs");
const os = require('os');
const compressing = require("compressing");

//Linux系统上'Linux'
//macOS 系统上'Darwin'
//Windows系统上'Windows_NT'
let sysType = os.type();

Date.prototype.Format = function(fmt) {
  var o = {
      "M+" : this.getMonth() + 1,
      "d+" : this.getDate(),
      "h+" : this.getHours(),
      "m+" : this.getMinutes(),
      "s+" : this.getSeconds(),
      "q+" : Math.floor((this.getMonth() + 3) / 3),
      "S" : this.getMilliseconds()
  };
  if (/(y+)/.test(fmt))
      fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o)
  if (new RegExp("(" + k + ")").test(fmt))
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}

function clean(zipPath){
  if(fs.existsSync(zipPath)){
    fs.unlinkSync(zipPath);
  }
}

const runSpawn = async function (project){
  await new Promise(function(resolve,reject){
    let ls;
    if(sysType==="Windows_NT"){
      ls = spawn("cmd.exe", ['/c', 'gulp', 'publish', '--project',project,'--env','prov'] );
    }else{
      ls = spawn("gulp", ['publish', '--project',project,'--env','prov'] );
    }
    ls.stdout.on('data', (data) => {
      console.log(` ${data}`);
    });

    ls.stderr.on('data', (data) => {
      console.log(`stderr: ${data}`);
      reject();
    });

    ls.on('close', (code) => {
      console.log(`child process exited with code ${code}`);
      //要压缩的目录
      let zippath = path.resolve(__dirname,"../dist/"+project.replace("T_",""));
      let pkg = require(zippath+"/package.json");
      //压缩包的存放目录
      let date = new Date();
      let zipname = pkg.name+"_"+date.Format("yyyyMMdd hh-mm-ss");
      let zipdir = path.resolve(__dirname,"../zip/"+zipname+".zip");
      clean(zipdir); //删除原有的包

      const tarStream = new compressing.zip.Stream();
      fs.readdir(zippath,function(err,files){
        if(err){
          console.log("======文件打开异常======");
          console.log(err);
          reject();
        }
        for(let i=0;i<files.length;i++){
          tarStream.addEntry(zippath+"/"+files[i]);
        }
        let writeStream = fs.createWriteStream(zipdir);
        tarStream.pipe(writeStream);
        writeStream.on('close', () => {
            console.log(`模板 ${pkg.name} 打包已完成！`);
            resolve();
        })
      });

    });

  });
}

let projects = "";
if(process.argv.length<3){
  console.log("缺少参数");
  return;
}
let exec = async function(){
  projects = process.argv[2];
  //压缩全部模板
  if(projects==="all"){
    let srcpath = path.resolve(__dirname,"../src/");
    let list = fs.readdirSync(srcpath);
    for(let i=0;i<list.length;i++){
      let pro = list[i];
      if(pro.includes("T_")){
        await runSpawn(pro);
      }
    }
  }else{
    let list = process.argv[2].split(",");
    console.log(list);

    for(let i=0;i<list.length;i++){
      let pro = 'T_' + list[i];
      await runSpawn(pro);
    }
  }
}

exec();



